import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:medpulse/providers/user_provider.dart';
import 'package:medpulse/providers/courses_provider.dart';
import 'package:medpulse/screens/error_screen.dart';

/// Screen that displays a summary of the selected subscription plan
/// and asks for confirmation before proceeding to payment
class OrderSummaryScreen extends ConsumerWidget {
  final String planType;
  final VoidCallback onProceedToPayment;
  final VoidCallback onChangePlan;

  const OrderSummaryScreen({
    super.key,
    required this.planType,
    required this.onProceedToPayment,
    required this.onChangePlan,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    // Get user data to access course and year information
    final userAsync = ref.watch(userProvider);
    final coursesAsync = ref.watch(coursesProvider);

    if (userAsync.isLoading || coursesAsync.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (userAsync.hasError) {
      return ErrorScreen(
        context: context,
        description: 'Error loading user data. Please try again.',
        onPressed: () {
          ref.invalidate(userProvider);
        },
      );
    }

    if (coursesAsync.hasError) {
      return ErrorScreen(
        context: context,
        description: 'Error loading course data. Please try again.',
        onPressed: () {
          ref.invalidate(coursesProvider);
        },
      );
    }

    // Plan details based on the selected plan type
    final Map<String, dynamic> planDetails = _getPlanDetails(planType);
    final String planTitle = planDetails['title'];
    final double planPrice = planDetails['price'];
    final String planDuration = planDetails['duration'];
    final IconData planIcon = planDetails['icon'];
    final bool isLifetime = planType == 'lifetime';

    // Format the price
    final formattedPrice = NumberFormat.currency(symbol: 'PKR ', decimalDigits: 0).format(planPrice);

    // Calculate per month pricing for comparison (if not lifetime)
    String perMonthText = '';
    if (!isLifetime && planDetails['months'] > 0) {
      final double monthlyPrice = planPrice / planDetails['months'];
      perMonthText = '(${NumberFormat.currency(symbol: 'PKR ', decimalDigits: 0).format(monthlyPrice)}/month)';
    }

    // Get course and year information
    String? courseInfo;

    // Check if user and courses data is available
    final user = userAsync.value!;
    final courses = coursesAsync.value!;

    // Verify that course and year are selected
    if (user.courseId != null && user.yearId != null) {
      final course = courses.courses[user.courseId];
      if (course != null) {
        if (user.yearId != null) {
          final year = course.years[user.yearId];
          if (year != null) {
            courseInfo = '${user.courseId} - ${user.yearId}';
          }
        }
      }
    }

    if (courseInfo == null) {
      return ErrorScreen(
        context: context,
        description: 'Error assembling course data. Please try again.',
        onPressed: () {
          ref.invalidate(coursesProvider);
        },
      );
    }

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600),
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Order Summary', style: theme.textTheme.headlineSmall),
                    const SizedBox(height: 24),

                    // Course information card
                    Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(color: theme.colorScheme.secondary, width: 2),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.school, size: 28, color: theme.colorScheme.secondary),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text('Subscription For', style: theme.textTheme.titleMedium),
                                      Text(
                                        courseInfo,
                                        style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Selected plan card
                    Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(color: theme.colorScheme.primary, width: 2),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(planIcon, size: 28, color: theme.colorScheme.primary),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text('Selected Plan', style: theme.textTheme.titleMedium),
                                      Text(
                                        planTitle,
                                        style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                                      ),
                                    ],
                                  ),
                                ),
                                TextButton.icon(
                                  onPressed: onChangePlan,
                                  icon: const Icon(Icons.edit),
                                  label: const Text('Change'),
                                ),
                              ],
                            ),
                            const Divider(height: 24),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('Subscription Duration:', style: theme.textTheme.bodyLarge),
                                Text(
                                  planDuration,
                                  style: theme.textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),

                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('Total Amount:', style: theme.textTheme.bodyLarge),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Text(
                                      formattedPrice,
                                      style: theme.textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.primary,
                                      ),
                                    ),
/*                                    if (perMonthText.isNotEmpty)
                                      Text(
                                        perMonthText,
                                        style: theme.textTheme.bodySmall?.copyWith(
                                          color: theme.colorScheme.onSurface.withAlpha(179),
                                        ),
                                      ),*/
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // What's included section
/*
                    Text('What\'s included for $courseInfo:', style: theme.textTheme.titleMedium),
                    const SizedBox(height: 12),
                    _buildFeatureItem(context, 'Full access to MCQ and Flipcard questions', Icons.check_circle),
                    _buildFeatureItem(context, 'Bookmark questions for recap', Icons.check_circle),
                    _buildFeatureItem(context, 'Access to new and updated content', Icons.check_circle),

                    const SizedBox(height: 24),
*/

                    // Payment information
                    Card(
                      elevation: 1,
                      color: theme.colorScheme.surface,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(color: theme.colorScheme.outline.withAlpha(77)),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.info_outline, color: theme.colorScheme.primary),
                                const SizedBox(width: 8),
                                Text('Payment Information', style: theme.textTheme.titleMedium),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'You will be redirected to our secure payment gateway to complete your purchase. Your subscription will be activated immediately after successful payment.',
                              style: theme.textTheme.bodyMedium,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(16.0),
              child: SizedBox(
                width: double.infinity,
                child: FilledButton.icon(
                  onPressed: onProceedToPayment,
                  icon: const Icon(Icons.payment),
                  label: const Text('Proceed to Payment'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(BuildContext context, String text, IconData icon) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, color: theme.colorScheme.primary, size: 20),
          const SizedBox(width: 12),
          Expanded(child: Text(text, style: theme.textTheme.bodyLarge)),
        ],
      ),
    );
  }

  Map<String, dynamic> _getPlanDetails(String planType) {
    switch (planType) {
      case '3_months':
        return {'title': '3 Months', 'price': 998.0, 'duration': '3 Months', 'months': 3, 'icon': Icons.calendar_today};
      case '6_months':
        return {
          'title': '6 Months',
          'price': 1798.0,
          'duration': '6 Months',
          'months': 6,
          'icon': Icons.calendar_month,
        };
      case '12_months':
        return {
          'title': '12 Months',
          'price': 2998.0,
          'duration': '12 Months',
          'months': 12,
          'icon': Icons.event_available,
        };
      case 'lifetime':
        return {
          'title': 'Lifetime',
          'price': 4998.0,
          'duration': 'Lifetime Access',
          'months': 0,
          'icon': Icons.all_inclusive,
        };
      default:
        return {'title': 'Unknown Plan', 'price': 0.0, 'duration': 'Unknown', 'months': 0, 'icon': Icons.help_outline};
    }
  }
}
