import 'package:entities/published_test_entity.dart';
import 'package:entities/question_entity.dart';
import 'package:entities/subject_entity.dart';
import 'package:entities/test_enums.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/widgets/constrained_width_app_bar.dart';
import 'package:medpulse/widgets/header_widget.dart';

import '../providers/test_access_mode_provider.dart';
import 'question_test_screen.dart';

class TestListScreen extends ConsumerWidget {
  final SubjectEntity subject;
  final String subjectName;
  final QuestionType questionType;

  const TestListScreen({
    super.key,
    required this.subject,
    required this.subjectName,
    required this.questionType,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: ConstrainedWidthAppBar(
        appBar: AppBar(
          title: Text('$subjectName Tests'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with image and message
              HeaderWidget(
                imagePath:
                    questionType == QuestionType.mcq
                        ? 'assets/images/onboarding_2.png'
                        : 'assets/images/onboarding_2.png',
                title:
                    questionType == QuestionType.mcq
                        ? 'MCQ Tests'
                        : 'FlipCard Tests',
                subtitle: 'Select a test to start practicing',
              ),
              const SizedBox(height: 32),

              // Tests list
              _TestsListSection(subject: subject, questionType: questionType),
            ],
          ),
        ),
      ),
    );
  }
}

// Main section for displaying tests
class _TestsListSection extends StatelessWidget {
  final SubjectEntity subject;
  final QuestionType questionType;

  const _TestsListSection({required this.subject, required this.questionType});

  @override
  Widget build(BuildContext context) {
    // Get tests directly from the subject (already filtered by question type)
    var tests = subject.tests.values.toList();
    tests.sort((a, b) => a.name.compareTo(b.name));

    if (tests.isEmpty) {
      return const _EmptyTestsList();
    }
    return _TestsList(tests: tests, questionType: questionType);
  }
}

// Widget to display when there are no tests
class _EmptyTestsList extends StatelessWidget {
  final double maxWidth;

  const _EmptyTestsList({this.maxWidth = 600});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: maxWidth),
        child: Card(
          margin: const EdgeInsets.symmetric(vertical: 32.0, horizontal: 16.0),
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.quiz_outlined,
                  size: 64,
                  color: Theme.of(context).colorScheme.secondary.withAlpha(
                    128,
                  ), // 0.5 * 255 = 127.5, rounded to 128
                ),
                const SizedBox(height: 16),
                Text(
                  'No tests available',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'There are no MCQ tests available for this subject yet',
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Widget to display the list of tests
class _TestsList extends ConsumerWidget {
  final List<PublishedTestEntity> tests;
  final double maxWidth;
  final QuestionType questionType;

  const _TestsList({
    required this.tests,
    required this.questionType,
    this.maxWidth = 600,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: maxWidth),
        child: ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: tests.length,
          itemBuilder: (context, index) {
            final test = tests[index];
            return _TestCard(test: test, questionType: questionType);
          },
        ),
      ),
    );
  }
}

// Helper method to get difficulty color
Color _getDifficultyColor(BuildContext context, TestDifficulty difficulty) {
  switch (difficulty) {
    case TestDifficulty.easy:
      return Colors.green;
    case TestDifficulty.medium:
      return Colors.orange;
    case TestDifficulty.hard:
      return Colors.red;
  }
}

// Helper method to format duration
String _formatDuration(int minutes) {
  if (minutes < 60) {
    return '$minutes mins';
  } else {
    final hours = minutes ~/ 60;
    final remainingMinutes = minutes % 60;
    if (remainingMinutes == 0) {
      return '$hours hr';
    } else {
      return '$hours hr $remainingMinutes mins';
    }
  }
}

// Card widget for each test
class _TestCard extends ConsumerWidget {
  final PublishedTestEntity test;
  final QuestionType questionType;

  const _TestCard({required this.test, required this.questionType});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToTestStart(context, ref, test.id!, test.name),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Icon(
                        Icons.quiz_outlined,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                        size: 32,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          test.name,
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Tap to start this test',
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface
                                .withAlpha(153), // 0.6 * 255 = 153
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.chevron_right,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Questions count
                  Chip(
                    label: Text('${test.questionCount} Questions'),
                    avatar: const Icon(Icons.help_outline, size: 16),
                    backgroundColor:
                        Theme.of(context).colorScheme.surfaceVariant,
                    labelStyle: Theme.of(context).textTheme.bodySmall,
                  ),
                  // Duration
                  Chip(
                    label: Text(_formatDuration(test.duration)),
                    avatar: const Icon(Icons.timer_outlined, size: 16),
                    backgroundColor:
                        Theme.of(context).colorScheme.surfaceVariant,
                    labelStyle: Theme.of(context).textTheme.bodySmall,
                  ),
                  // Difficulty
                  Chip(
                    label: Text(test.difficulty.name.toUpperCase()),
                    backgroundColor: _getDifficultyColor(
                      context,
                      test.difficulty,
                    ).withAlpha(51),
                    // 0.2 * 255 = 51
                    side: BorderSide(
                      color: _getDifficultyColor(context, test.difficulty),
                      width: 1,
                    ),
                    labelStyle: TextStyle(
                      color: _getDifficultyColor(context, test.difficulty),
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToTestStart(
    BuildContext context,
    WidgetRef ref,
    String testId,
    String name,
  ) {
    _checkAccessModeAndNavigate(context, ref, testId, name);
  }

  Future<void> _checkAccessModeAndNavigate(
    BuildContext context,
    WidgetRef ref,
    String testId,
    String name,
  ) async {
    // Show loading indicator
    final loadingOverlay = _showLoadingOverlay(context);

    try {
      // Check access mode
      final accessMode = await ref.read(testAccessModeProvider(testId).future);

      // Close loading indicator
      loadingOverlay.remove();

      // Navigate to test screen with appropriate mode
      if (context.mounted) {
        Navigator.of(context, rootNavigator: true).push(
          MaterialPageRoute(
            builder:
                (context) => QuestionTestScreen(
                  testId: test.id!,
                  name: name,
                  free: accessMode == TestAccessMode.free,
                  questionType: questionType,
                ),
          ),
        );
      }
    } catch (error, stack) {
      // Close loading indicator
      loadingOverlay.remove();

      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error checking subscription: ${error.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  OverlayEntry _showLoadingOverlay(BuildContext context) {
    final overlay = OverlayEntry(
      builder:
          (context) => Container(
            color: Colors.black.withAlpha(128),
            // 0.5 * 255 = 127.5, rounded to 128
            child: const Center(child: CircularProgressIndicator()),
          ),
    );

    Overlay.of(context).insert(overlay);
    return overlay;
  }
}
