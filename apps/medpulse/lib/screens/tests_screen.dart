import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:medpulse/screens/test_list_screen.dart';
import 'package:medpulse/widgets/header_widget.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:entities/subject_entity.dart';
import 'package:entities/question_entity.dart';

import '../providers/courses_provider.dart';
import '../providers/user_provider.dart';

part 'tests_screen.g.dart';

// Provider to fetch available subjects based on user's course and year
@riverpod
class AvailableSubjects extends _$AvailableSubjects {
  @override
  Future<List<SubjectEntity>> build() async {
    // Get user data
    final userAsync = await ref.watch(userProvider.future);
    if (userAsync == null || userAsync.courseId == null || userAsync.yearId == null) {
      return [];
    }

    // Get courses data
    final coursesAsync = await ref.watch(coursesProvider.future);
    if (coursesAsync == null) {
      return [];
    }

    // Extract the course
    final course = coursesAsync.courses[userAsync.courseId];
    if (course == null) {
      return [];
    }

    // Extract the year
    final year = course.years[userAsync.yearId];
    if (year == null) {
      return [];
    }

    // Return the subjects as a list
    var subjectEntries = year.subjects.entries.toList();
    subjectEntries.sort((a, b) => a.key.compareTo(b.key));

    return subjectEntries.map((entry) => entry.value).toList();
  }
}

// Provider for selected subject
@riverpod
class SelectedSubject extends _$SelectedSubject {
  @override
  SubjectEntity? build() => null;

  void setSubject(SubjectEntity? subject) => state = subject;
}

class TestsScreen extends ConsumerWidget {
  final QuestionType questionType;

  const TestsScreen({super.key, required this.questionType});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Set title and subtitle based on question type
    final String subtitle =
        questionType == QuestionType.mcq
            ? 'Choose a subject to view available MCQ tests.'
            : 'Choose a subject to view available FlipCard tests.';

    final String imagePath = 'assets/images/onboarding_2.png';

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with image and message
            HeaderWidget(
              imagePath: imagePath,
              title: null, // Will show "Welcome, [User]"
              subtitle: subtitle,
            ),
            const SizedBox(height: 32),

            // Subjects list
            _SubjectsListSection(questionType: questionType),
          ],
        ),
      ),
    );
  }
}

// Main section for displaying subjects
class _SubjectsListSection extends ConsumerWidget {
  final QuestionType questionType;

  const _SubjectsListSection({required this.questionType});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final subjectsAsync = ref.watch(availableSubjectsProvider);

    return subjectsAsync.when(
      data: (subjects) {
        if (subjects.isEmpty) {
          return const _EmptySubjectsList();
        }
        return _SubjectsList(subjects: subjects, questionType: questionType);
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _ErrorDisplay(message: error.toString()),
    );
  }
}

// Widget to display when there are no subjects
class _EmptySubjectsList extends StatelessWidget {
  final double maxWidth;

  const _EmptySubjectsList({this.maxWidth = 600});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: maxWidth),
        child: Card(
          margin: const EdgeInsets.symmetric(vertical: 32.0, horizontal: 16.0),
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.subject_outlined,
                  size: 64,
                  color: Theme.of(context).colorScheme.secondary.withAlpha(128), // 0.5 * 255 = 127.5, rounded to 128
                ),
                const SizedBox(height: 16),
                Text('No subjects available', style: Theme.of(context).textTheme.titleLarge),
                const SizedBox(height: 8),
                Text(
                  'Please select a course and year in your profile',
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Widget to display the list of subjects
class _SubjectsList extends StatelessWidget {
  final List<SubjectEntity> subjects;
  final double maxWidth;
  final QuestionType questionType;

  const _SubjectsList({required this.subjects, required this.questionType, this.maxWidth = 600});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: maxWidth),
        child: ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: subjects.length,
          itemBuilder: (context, index) {
            final subject = subjects[index];
            // For now, use a placeholder name since we need to restructure to get the actual name
            final subjectName = 'Subject ${index + 1}'; // TODO: Get actual subject name from map key
            return _SubjectCard(
              subject: subject,
              subjectName: subjectName,
              questionType: questionType
            );
          },
        ),
      ),
    );
  }
}

// Card widget for each subject
// Card widget for each subject
// Card widget for each subject
class _SubjectCard extends ConsumerWidget {
  final SubjectEntity subject;
  final String subjectName;
  final QuestionType questionType;

  const _SubjectCard({required this.subject, required this.subjectName, required this.questionType});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Filter tests by question type and count available tests
    final filteredTests = subject.tests.values.where((test) => test.type == questionType).toList();
    final testsCount = filteredTests.length;

    // If no tests available for this question type, don't show the subject
    if (testsCount == 0) {
      return const SizedBox.shrink();
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          ref.read(selectedSubjectProvider.notifier).setSubject(subject);
          _navigateToTestsList(context);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Icon(
                    questionType == QuestionType.mcq ? Icons.quiz_outlined : Icons.flip_outlined,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                    size: 32,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(subjectName, style: Theme.of(context).textTheme.titleMedium),
                    const SizedBox(height: 4),
                    Text(
                      '$testsCount tests available',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withAlpha(153), // 0.6 * 255 = 153
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.chevron_right, color: Theme.of(context).colorScheme.onSurfaceVariant),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToTestsList(BuildContext context) {
    // Get the questionType from the parent widget
    final questionType =
        context.findAncestorWidgetOfExactType<_SubjectsListSection>()?.questionType ?? QuestionType.mcq;

    // Filter tests by question type
    final filteredTests = Map.fromEntries(subject.tests.entries.where((entry) => entry.value.type == questionType));

    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => TestListScreen(
              subject: subject.copyWithTests(filteredTests),
              subjectName: subjectName,
              questionType: questionType
            ),
      ),
    );
  }
}

// Error display widget
class _ErrorDisplay extends StatelessWidget {
  final String message;
  final double maxWidth;

  const _ErrorDisplay({required this.message, this.maxWidth = 600});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: maxWidth),
        child: Card(
          margin: const EdgeInsets.symmetric(vertical: 32.0, horizontal: 16.0),
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Theme.of(context).colorScheme.error),
                const SizedBox(height: 16),
                Text('Error loading subjects', style: Theme.of(context).textTheme.titleLarge),
                const SizedBox(height: 8),
                Text(message, style: Theme.of(context).textTheme.bodyMedium, textAlign: TextAlign.center),
                const SizedBox(height: 16),
                FilledButton.tonal(
                  onPressed: () {
                    // TODO: Implement retry logic
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
