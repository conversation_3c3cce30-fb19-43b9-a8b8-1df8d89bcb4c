// apps/admin/lib/main.dart

import 'package:admin/shared/providers/app_router_provider.dart';
import 'package:admin/shared/widgets/status_messages.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/connectivity_provider.dart';
import 'package:providers/environment_config.dart';
import 'package:providers/firebase_app_provider.dart';
import 'package:providers/scaffold_messenger_key_provider.dart';

import 'config/theme.dart';
import 'firebase_options.dart' as prod_options;
import 'firebase_options_dev.dart' as dev_options;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Print environment info
  EnvironmentConfig.printEnvironmentInfo();

  // Get Firebase options based on environment
  final firebaseOptions = EnvironmentConfig.isDevelopment
      ? dev_options.DefaultFirebaseOptions.currentPlatform
      : prod_options.DefaultFirebaseOptions.currentPlatform;

  // Initialize Firebase
  await Firebase.initializeApp(options: firebaseOptions);

  ProvidersFirebaseOptions(firebaseOptions);

  runApp(const ProviderScope(child: AdminPanelApp()));
}

class AdminPanelApp extends ConsumerWidget {
  const AdminPanelApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final firebaseAppState = ref.watch(firebaseAppProvider);
    final isConnected = ref.watch(connectivityProvider).value ?? false;
    final scaffoldKey = ref.watch(scaffoldMessengerKeyProvider);

    if (firebaseAppState.isLoading) {
      return MaterialApp(
        theme: AppTheme.lightTheme,
        scaffoldMessengerKey: scaffoldKey,
        home: const Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    if (firebaseAppState.hasError) {
      debugPrint('Firebase initialization error: ${firebaseAppState.error}');
      return MaterialApp(
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        scaffoldMessengerKey: scaffoldKey,
        home: ErrorMessage(
          title: 'Connection Issue',
          message:
              'We\'re having trouble connecting to our services. Please check your connection and try again.',
          onRetry: () {
            ref.invalidate(firebaseAppProvider);
          },
          icon: Icons.cloud_off,
        ),
      );
    }

    if (!isConnected) {
      return MaterialApp(
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        scaffoldMessengerKey: scaffoldKey,
        home: ErrorMessage(
          title: 'No Internet Connection',
          message:
              'This app requires an internet connection to function properly. Please check your connection and try again.',
          onRetry: () {
            ref.invalidate(connectivityProvider);
          },
          icon: Icons.wifi_off_rounded,
        ),
      );
    }

    return MaterialApp.router(
      title: 'Medpulse Admin',
      theme: AppTheme.lightTheme,
      scaffoldMessengerKey: scaffoldKey,
      routerConfig: ref.watch(appRouterProvider),
      debugShowCheckedModeBanner: false,
    );
  }
}
