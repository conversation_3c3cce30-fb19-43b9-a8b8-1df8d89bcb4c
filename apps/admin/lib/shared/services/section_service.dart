// apps/admin/lib/shared/services/section_service.dart

import 'package:cloud_firestore/cloud_firestore.dart';

class SectionService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Add a new section to a subject
  static Future<void> addSectionToSubject({
    required String courseId,
    required String yearId,
    required String subjectId,
    required String sectionName,
  }) async {
    if (sectionName.trim().isEmpty) return;

    final trimmedSection = sectionName.trim();
    
    // Get the subject document path
    final subjectDocPath = 'app_config/courses';
    final docRef = _firestore.doc(subjectDocPath);

    await _firestore.runTransaction((transaction) async {
      final snapshot = await transaction.get(docRef);
      
      if (!snapshot.exists) {
        throw Exception('Courses document not found');
      }

      final data = snapshot.data()!;
      
      // Navigate to the specific subject
      final courses = data['courses'] as Map<String, dynamic>? ?? {};
      final course = courses[courseId] as Map<String, dynamic>? ?? {};
      final years = course['years'] as Map<String, dynamic>? ?? {};
      final year = years[yearId] as Map<String, dynamic>? ?? {};
      final subjects = year['subjects'] as Map<String, dynamic>? ?? {};
      final subject = subjects[subjectId] as Map<String, dynamic>? ?? {};
      
      // Get current sections list
      final currentSections = List<String>.from(subject['sections'] ?? []);

      // Check if section already exists (case-insensitive)
      final sectionExists = currentSections.any(
        (section) => section.toLowerCase() == trimmedSection.toLowerCase(),
      );

      if (!sectionExists) {
        // Add the new section
        currentSections.add(trimmedSection);

        // Update the nested structure
        final updatedSubject = Map<String, dynamic>.from(subject);
        updatedSubject['sections'] = currentSections;
        
        final updatedSubjects = Map<String, dynamic>.from(subjects);
        updatedSubjects[subjectId] = updatedSubject;
        
        final updatedYear = Map<String, dynamic>.from(year);
        updatedYear['subjects'] = updatedSubjects;
        
        final updatedYears = Map<String, dynamic>.from(years);
        updatedYears[yearId] = updatedYear;
        
        final updatedCourse = Map<String, dynamic>.from(course);
        updatedCourse['years'] = updatedYears;
        
        final updatedCourses = Map<String, dynamic>.from(courses);
        updatedCourses[courseId] = updatedCourse;
        
        final updatedData = Map<String, dynamic>.from(data);
        updatedData['courses'] = updatedCourses;
        
        // Write back to Firestore
        transaction.update(docRef, updatedData);
      }
    });
  }

  /// Remove a section from a subject
  static Future<void> removeSectionFromSubject({
    required String courseId,
    required String yearId,
    required String subjectId,
    required String sectionName,
  }) async {
    final subjectDocPath = 'app_config/courses';
    final docRef = _firestore.doc(subjectDocPath);

    await _firestore.runTransaction((transaction) async {
      final snapshot = await transaction.get(docRef);
      
      if (!snapshot.exists) {
        throw Exception('Courses document not found');
      }

      final data = snapshot.data()!;
      
      // Navigate to the specific subject
      final courses = data['courses'] as Map<String, dynamic>? ?? {};
      final course = courses[courseId] as Map<String, dynamic>? ?? {};
      final years = course['years'] as Map<String, dynamic>? ?? {};
      final year = years[yearId] as Map<String, dynamic>? ?? {};
      final subjects = year['subjects'] as Map<String, dynamic>? ?? {};
      final subject = subjects[subjectId] as Map<String, dynamic>? ?? {};
      
      // Get current sections list
      final currentSections = List<String>.from(subject['sections'] ?? []);
      
      // Remove the section (case-insensitive)
      currentSections.removeWhere(
        (section) => section.toLowerCase() == sectionName.toLowerCase(),
      );
      
      // Update the nested structure
      final updatedSubject = Map<String, dynamic>.from(subject);
      updatedSubject['sections'] = currentSections;
      
      final updatedSubjects = Map<String, dynamic>.from(subjects);
      updatedSubjects[subjectId] = updatedSubject;
      
      final updatedYear = Map<String, dynamic>.from(year);
      updatedYear['subjects'] = updatedSubjects;
      
      final updatedYears = Map<String, dynamic>.from(years);
      updatedYears[yearId] = updatedYear;
      
      final updatedCourse = Map<String, dynamic>.from(course);
      updatedCourse['years'] = updatedYears;
      
      final updatedCourses = Map<String, dynamic>.from(courses);
      updatedCourses[courseId] = updatedCourse;
      
      final updatedData = Map<String, dynamic>.from(data);
      updatedData['courses'] = updatedCourses;
      
      // Write back to Firestore
      transaction.update(docRef, updatedData);
    });
  }

  /// Get all sections for a subject
  static Future<List<String>> getSectionsForSubject({
    required String courseId,
    required String yearId,
    required String subjectId,
  }) async {
    try {
      final subjectDocPath = 'app_config/courses';
      final snapshot = await _firestore.doc(subjectDocPath).get();

      if (!snapshot.exists) return [];

      final data = snapshot.data()!;
      final courses = data['courses'] as Map<String, dynamic>? ?? {};
      final course = courses[courseId] as Map<String, dynamic>? ?? {};
      final years = course['years'] as Map<String, dynamic>? ?? {};
      final year = years[yearId] as Map<String, dynamic>? ?? {};
      final subjects = year['subjects'] as Map<String, dynamic>? ?? {};
      final subject = subjects[subjectId] as Map<String, dynamic>? ?? {};

      final sectionsWithTopics = subject['sectionsWithTopics'] as Map<String, dynamic>? ?? {};
      return sectionsWithTopics.keys.toList();
    } catch (e) {
      return [];
    }
  }

  /// Get all topics for a specific section
  static Future<List<String>> getTopicsForSection({
    required String courseId,
    required String yearId,
    required String subjectId,
    required String sectionName,
  }) async {
    try {
      final subjectDocPath = 'app_config/courses';
      final snapshot = await _firestore.doc(subjectDocPath).get();

      if (!snapshot.exists) return [];

      final data = snapshot.data()!;
      final courses = data['courses'] as Map<String, dynamic>? ?? {};
      final course = courses[courseId] as Map<String, dynamic>? ?? {};
      final years = course['years'] as Map<String, dynamic>? ?? {};
      final year = years[yearId] as Map<String, dynamic>? ?? {};
      final subjects = year['subjects'] as Map<String, dynamic>? ?? {};
      final subject = subjects[subjectId] as Map<String, dynamic>? ?? {};

      final sectionsWithTopics = subject['sectionsWithTopics'] as Map<String, dynamic>? ?? {};
      return List<String>.from(sectionsWithTopics[sectionName] ?? []);
    } catch (e) {
      return [];
    }
  }

  /// Check if a section exists in a subject
  static Future<bool> sectionExists({
    required String courseId,
    required String yearId,
    required String subjectId,
    required String sectionName,
  }) async {
    try {
      final sections = await getSectionsForSubject(
        courseId: courseId,
        yearId: yearId,
        subjectId: subjectId,
      );
      
      return sections.any(
        (section) => section.toLowerCase() == sectionName.toLowerCase(),
      );
    } catch (e) {
      return false;
    }
  }
}
