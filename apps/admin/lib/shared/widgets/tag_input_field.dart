﻿
import 'package:flutter/material.dart';

class TagInputField extends StatefulWidget {
  final String label;
  final List<String> tags;
  final ValueChanged<List<String>> onTagsChanged;
  final String? hintText;
  final bool enabled;
  final String? Function(String)? validator;

  const TagInputField({
    super.key,
    required this.label,
    required this.tags,
    required this.onTagsChanged,
    this.hintText,
    this.enabled = true,
    this.validator,
  });

  @override
  State<TagInputField> createState() => _TagInputFieldState();
}

class _TagInputFieldState extends State<TagInputField> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _addTag(String value) {
    final trimmedValue = value.trim();
    if (trimmedValue.isEmpty || widget.tags.contains(trimmedValue)) {
      return;
    }

    if (widget.validator != null) {
      final error = widget.validator!(trimmedValue);
      if (error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(error)),
        );
        return;
      }
    }

    final newTags = [...widget.tags, trimmedValue];
    widget.onTagsChanged(newTags);
    _controller.clear();
  }

  void _removeTag(String tag) {
    final newTags = widget.tags.where((t) => t != tag).toList();
    widget.onTagsChanged(newTags);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.label,
          style: theme.textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(
              color: _focusNode.hasFocus 
                  ? theme.colorScheme.primary 
                  : theme.colorScheme.outline,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              ...widget.tags.map((tag) => _TagChip(
                label: tag,
                onDeleted: widget.enabled ? () => _removeTag(tag) : null,
              )),
              
              if (widget.enabled)
                SizedBox(
                  width: 200,
                  child: TextField(
                    controller: _controller,
                    focusNode: _focusNode,
                    decoration: InputDecoration(
                      hintText: widget.hintText ?? 'Type and press Enter',
                      border: InputBorder.none,
                      isDense: true,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                    ),
                    onSubmitted: _addTag,
                    onChanged: (value) {
                      if (value.contains(',') || value.contains(';')) {
                        final cleanValue = value.replaceAll(RegExp(r'[,;]'), '');
                        _addTag(cleanValue);
                      }
                    },
                  ),
                ),
            ],
          ),
        ),
        
        if (widget.enabled)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              'Type and press Enter to add. Use comma or semicolon to separate multiple items.',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
      ],
    );
  }
}

class _TagChip extends StatelessWidget {
  final String label;
  final VoidCallback? onDeleted;

  const _TagChip({
    required this.label,
    this.onDeleted,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onPrimaryContainer,
            ),
          ),
          if (onDeleted != null) ...[
            const SizedBox(width: 4),
            GestureDetector(
              onTap: onDeleted,
              child: Icon(
                Icons.close,
                size: 16,
                color: theme.colorScheme.onPrimaryContainer,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class ExpandableTagSection extends StatefulWidget {
  final String title;
  final Widget child;
  final bool initiallyExpanded;
  final bool enabled;

  const ExpandableTagSection({
    super.key,
    required this.title,
    required this.child,
    this.initiallyExpanded = true,
    this.enabled = true,
  });

  @override
  State<ExpandableTagSection> createState() => _ExpandableTagSectionState();
}

class _ExpandableTagSectionState extends State<ExpandableTagSection> {
  late bool _isExpanded;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        children: [
          InkWell(
            onTap: widget.enabled ? () => setState(() => _isExpanded = !_isExpanded) : null,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    widget.title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  if (!widget.enabled)
                    Icon(
                      Icons.lock_outline,
                      size: 16,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                ],
              ),
            ),
          ),
          
          if (_isExpanded)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: widget.child,
            ),
        ],
      ),
    );
  }
}
