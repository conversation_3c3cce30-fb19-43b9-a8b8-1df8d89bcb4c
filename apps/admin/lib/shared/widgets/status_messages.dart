﻿
import 'package:flutter/material.dart';

class FilterSelectionMessage extends StatelessWidget {
  const FilterSelectionMessage({super.key});

  @override
  Widget build(BuildContext context) {
    return const MessageDisplay(
      icon: Icons.filter_list,
      title: "Select All Required Filters",
      message: "Please select Course, Year, Subject, Type, and Difficulty to view available questions.",
    );
  }
}

class LoadingMessage extends StatelessWidget {
  final String message;

  const LoadingMessage({this.message = "Loading...", super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(message)
        ],
      ),
    );
  }
}

class ErrorMessage extends StatelessWidget {
  final VoidCallback? onRetry;
  final String title;
  final String message;
  final IconData icon;

  const ErrorMessage({
    this.onRetry,
    this.title = "Error Loading Data",
    this.message =
        "There was a problem retrieving the data. Please try again later.",
    this.icon = Icons.error_outline,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return MessageDisplay(
      icon: icon,
      title: title,
      message: message,
      showRetryButton: onRetry != null,
      onRetry: onRetry,
    );
  }
}

class EmptyDataMessage extends StatelessWidget {
  final String title;
  final String message;

  const EmptyDataMessage({
    this.title = "No Data Found",
    this.message =
        "No items match your current filters. Try changing your filter criteria or add a new item.",
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return MessageDisplay(
      icon: Icons.search_off,
      title: title,
      message: message,
    );
  }
}

class MessageDisplay extends StatelessWidget {
  final IconData icon;
  final String title;
  final String message;
  final bool showRetryButton;
  final VoidCallback? onRetry;

  const MessageDisplay({
    required this.icon,
    required this.title,
    required this.message,
    this.showRetryButton = false,
    this.onRetry,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
            Icon(
              icon,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
            ),
            if (showRetryButton && onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(
                  Icons.refresh,
                  color: Colors.white,
                ),
                label: const Text("Retry"),
              ),
            ],
          ],
        ),
        ),
      ),
    );
  }
}
