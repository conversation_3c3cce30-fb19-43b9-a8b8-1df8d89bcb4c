// lib/features/dashboard/widgets/app_navigation_bar.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Simple navigation item model
class NavigationItem {
  final String label;
  final IconData icon;
  final String route;

  const NavigationItem({
    required this.label,
    required this.icon,
    required this.route,
  });
}

class AppNavigationBar extends ConsumerWidget {
  const AppNavigationBar({super.key});

  /// Static navigation items - only for admin users
  static const List<NavigationItem> _navigationItems = [
    NavigationItem(
      label: 'Dashboard',
      icon: Icons.dashboard_outlined,
      route: '/dashboard',
    ),
    NavigationItem(
      label: 'Tests',
      icon: Icons.assignment_outlined,
      route: '/tests',
    ),
    NavigationItem(
      label: 'Courses',
      icon: Icons.school_outlined,
      route: '/courses',
    ),
  ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentLocation = GoRouterState.of(context).matchedLocation;

    return Container(
      color: const Color(0xFF454FBF),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // If there's enough space, use expanded layout
          if (constraints.maxWidth > 600) {
            return _buildExpandedLayout(context, currentLocation, _navigationItems);
          } else {
            return _buildCompactLayout(context, currentLocation, _navigationItems);
          }
        },
      ),
    );
  }

  Widget _buildExpandedLayout(
    BuildContext context,
    String currentLocation,
    List<NavigationItem> accessibleItems,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: accessibleItems.map((item) => Expanded(
          child: _NavItem(
            icon: item.icon,
            label: item.label,
            isSelected: currentLocation.startsWith(item.route),
            onTap: () => context.go(item.route),
          ),
        )).toList(),
      ),
    );
  }

  Widget _buildCompactLayout(
    BuildContext context,
    String currentLocation,
    List<NavigationItem> accessibleItems,
  ) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: accessibleItems.map((item) => _NavItem(
            icon: item.icon,
            label: item.label,
            isSelected: currentLocation.startsWith(item.route),
            onTap: () => context.go(item.route),
          )).toList(),
        ),
      ),
    );
  }
}

class _NavItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final bool isSelected;
  final VoidCallback onTap;

  const _NavItem({
    required this.icon,
    required this.label,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                label,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
