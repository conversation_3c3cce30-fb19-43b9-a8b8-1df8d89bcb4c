// lib/shared/providers/filter_helpers_provider.dart

import 'package:entities/course_entity.dart';
import 'package:entities/subject_entity.dart';
import 'package:entities/year_entity.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:providers/courses_provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'filter_provider.dart';

part 'filter_helpers_provider.g.dart';

/// Provider for available courses
@riverpod
Future<Map<String, CourseEntity>> availableCourses(Ref ref) async {
  final coursesData = await ref.watch(coursesProvider.future);
  return coursesData.courses;
}

/// Provider for available years based on selected course
@riverpod
Future<Map<String, YearEntity>> availableYears(Ref ref) async {
  final coursesData = await ref.watch(coursesProvider.future);
  final filter = ref.watch(filterProvider);

  if (filter.courseId == null) {
    return {};
  }

  final course = coursesData.courses[filter.courseId];
  if (course == null) {
    return {};
  }

  // Get years and sort them properly by year number
  final years = course.years;

  // Create a sorted map of years
  final sortedYears = Map<String, YearEntity>.fromEntries(years.entries.toList()
    ..sort((a, b) {
      // Extract year numbers from strings like "1st Year", "2nd Year", etc.
      final yearNumA = _extractYearNumber(a.key);
      final yearNumB = _extractYearNumber(b.key);
      return yearNumA.compareTo(yearNumB);
    }));

  return sortedYears;
}

// Helper function to extract year number from strings like "1st Year", "2nd Year", etc.
int _extractYearNumber(String yearString) {
  // Look for patterns like 1st, 2nd, 3rd, 4th, etc.
  final RegExp regex = RegExp(r'(\d+)');
  final match = regex.firstMatch(yearString);
  if (match != null && match.groupCount >= 1) {
    return int.tryParse(match.group(1) ?? '0') ?? 0;
  }
  return 0; // Default if no number found
}

/// Provider for available subjects based on selected course and year
@riverpod
Future<Map<String, SubjectEntity>> availableSubjects(Ref ref) async {
  final coursesData = await ref.watch(coursesProvider.future);
  final filter = ref.watch(filterProvider);

  if (filter.courseId == null || filter.yearId == null) {
    return {};
  }

  final course = coursesData.courses[filter.courseId];
  if (course == null) {
    return {};
  }

  final year = course.years[filter.yearId];
  if (year == null) {
    return {};
  }

  return year.subjects;
}

/// Provider for available topics based on selected course, year, and subject
@riverpod
Future<List<String>> availableTopics(Ref ref) async {
  final filter = ref.watch(filterProvider);

  if (filter.courseId == null || filter.yearId == null || filter.subjectId == null) {
    return [];
  }

  try {
    // Get subjects from the courses data
    final subjects = await ref.watch(availableSubjectsProvider.future);
    final subject = subjects[filter.subjectId];

    if (subject == null) {
      return [];
    }

    // Return topics from all sections in the subject entity
    final topics = <String>[];
    for (final section in subject.sections.values) {
      topics.addAll(section.topics.keys);
    }
    return topics..sort();
  } catch (e) {
    // Return empty list if there's any error
    return [];
  }
}
