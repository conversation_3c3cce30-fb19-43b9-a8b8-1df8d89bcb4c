// apps/admin/lib/shared/providers/app_router_provider.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../features/auth/providers/auth_provider.dart';
import '../../features/auth/screens/forgot_password_screen.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/courses/screens/courses_screen.dart';
import '../../features/courses/screens/course_form_screen.dart';
import '../../features/courses/screens/course_detail_screen.dart';
import '../../features/dashboard/screens/dashboard_screen.dart';
import '../../features/dashboard/screens/dashboard_main_screen.dart';

// Questions screens removed - questions are now created within test context
import '../../features/questions/screens/add_question_screen.dart';

import '../../features/tests/screens/create_test_screen.dart';
import '../../features/tests/screens/import_test_screen.dart';
import '../../features/tests/screens/tests_screen.dart';
import '../../features/tests/screens/view_test_screen.dart';



part 'app_router_provider.g.dart';

class AdminRouterNotifier extends ChangeNotifier {
  AdminRouterNotifier(this.ref) {
    // ✅ Watch the auth state provider for changes
    ref.listen(authStateProvider, (previous, next) {
      debugPrint('🔄 Router notifier: Auth state changed from $previous to $next');
      debugPrint('🔄 Router notifier: Triggering router refresh');
      notifyListeners();
    });
  }

  final Ref ref;
}

@riverpod
GoRouter appRouter(Ref ref) {
  final notifier = AdminRouterNotifier(ref);

  return GoRouter(
    initialLocation: '/',
    redirect: (context, state) {
      // ✅ Use the auth state provider
      final authAsync = ref.read(authStateProvider);

      debugPrint('🔄 Router redirect: Current route=${state.matchedLocation}, Auth state=${authAsync.runtimeType}');

      // ✅ Manual checking approach for complex router logic
      if (authAsync.isLoading) {
        debugPrint('🔄 Auth loading, protecting routes...');
        return _isProtectedRoute(state.matchedLocation) ? '/' : null;
      }

      if (authAsync.hasError) {
        debugPrint('🔴 Auth error: ${authAsync.error}');
        return '/';
      }

      if (authAsync.hasValue) {
        final result = _handleAuthenticatedUser(authAsync.value, state);
        debugPrint('🔄 Router redirect result for authenticated user: $result');
        return result;
      }

      debugPrint('⚠️ Unexpected auth state, redirecting to login');
      return '/';
    },
    refreshListenable: notifier,
    routes: [
      // Auth routes
      GoRoute(
        path: '/',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),

      // Dashboard Shell Route
      ShellRoute(
        builder: (context, state, child) => DashboardScreen(child: child),
        routes: [
          GoRoute(
            path: '/dashboard',
            builder: (context, state) => const DashboardMainScreen(),
          ),
          // Questions routes removed - questions are now created within test context
          GoRoute(
            path: '/tests',
            builder: (context, state) => const TestsScreen(),
          ),
          GoRoute(
            path: '/add-test',
            builder: (context, state) => const CreateTestScreen(),
          ),
          GoRoute(
            path: '/import-test',
            builder: (context, state) => const ImportTestScreen(),
          ),
          GoRoute(
            path: '/add-question',
            builder: (context, state) {
              final courseId = state.uri.queryParameters['courseId']!;
              final yearId = state.uri.queryParameters['yearId']!;
              final subjectId = state.uri.queryParameters['subjectId']!;
              final typeStr = state.uri.queryParameters['type']!;

              return AddQuestionScreen(
                courseId: courseId,
                yearId: yearId,
                subjectId: subjectId,
                type: typeStr,
              );
            },
          ),
          GoRoute(
            path: '/edit-question/:id',
            builder: (context, state) {
              final questionId = state.pathParameters['id']!;
              final courseId = state.uri.queryParameters['courseId']!;
              final yearId = state.uri.queryParameters['yearId']!;
              final subjectId = state.uri.queryParameters['subjectId']!;
              final typeStr = state.uri.queryParameters['type']!;

              return AddQuestionScreen(
                questionId: questionId,
                courseId: courseId,
                yearId: yearId,
                subjectId: subjectId,
                type: typeStr,
              );
            },
          ),
          GoRoute(
            path: '/edit-test/:id',
            builder: (context, state) {
              final testId = state.pathParameters['id'];
              return CreateTestScreen(testId: testId);
            },
          ),
          GoRoute(
            path: '/view-test/:id',
            builder: (context, state) {
              final testId = state.pathParameters['id'];
              return ViewTestScreen(testId: testId!);
            },
          ),
          GoRoute(
            path: '/courses',
            builder: (context, state) => const CoursesScreen(),
          ),
          GoRoute(
            path: '/add-course',
            builder: (context, state) => const CourseFormScreen(),
          ),
          GoRoute(
            path: '/edit-course/:id',
            builder: (context, state) {
              final courseId = state.pathParameters['id'];
              return CourseFormScreen(courseId: courseId);
            },
          ),
          GoRoute(
            path: '/view-course/:id',
            builder: (context, state) {
              final courseId = state.pathParameters['id'];
              return CourseDetailScreen(courseId: courseId!);
            },
          ),



        ],
      ),
    ],
    errorBuilder: (context, state) {
      debugPrint('Router Error: Could not navigate to ${state.matchedLocation}');
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'Page not found',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'The requested page "${state.matchedLocation}" could not be found.',
                textAlign: TextAlign.center,
                style: const TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => context.go('/'),
                child: const Text('Go to Login'),
              ),
              const SizedBox(height: 8),
              TextButton(
                onPressed: () => context.go('/dashboard'),
                child: const Text('Go to Dashboard'),
              ),
            ],
          ),
        ),
      );
    },
  );
}

// ========================================
// ROUTER HELPER FUNCTIONS
// ========================================

/// Check if a route requires admin authentication
bool _isProtectedRoute(String route) {
  final protectedRoutes = [
    '/dashboard',
    '/add-question', '/edit-question', // Question creation/editing within test context
    '/tests', '/add-test', '/edit-test', '/view-test', '/import-test',
    '/courses', '/add-course', '/edit-course', '/view-course',
  ];

  return protectedRoutes.any((protectedRoute) => route.startsWith(protectedRoute));
}

/// Handle authenticated user routing logic
/// Since only admins can authenticate, if user exists they have access to everything
String? _handleAuthenticatedUser(AdminUser? adminUser, GoRouterState state) {
  final currentRoute = state.matchedLocation;
  final isAuthRoute = currentRoute == '/' || currentRoute == '/forgot-password';

  if (adminUser == null) {
    // User is not authenticated
    if (!isAuthRoute) {
      debugPrint('🔴 Unauthenticated user accessing protected route: $currentRoute');
      return '/';
    }
    return null; // Allow access to auth routes
  }

  // ✅ User is authenticated (and therefore admin) - allow access to everything
  if (isAuthRoute) {
    debugPrint('✅ Authenticated admin on auth route, redirecting to dashboard');
    return '/dashboard';
  }

  // ✅ No need to check permissions - if user exists, they're admin
  return null; // Allow all navigation
}
