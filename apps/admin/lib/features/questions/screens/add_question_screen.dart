import 'package:entities/question_entity.dart';
import 'package:entities/test_enums.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../shared/widgets/app_button.dart';
import '../../../shared/providers/filter_provider.dart';
import '../../../shared/providers/filter_helpers_provider.dart';
import '../../../shared/services/topic_service.dart';
import 'package:providers/courses_provider.dart';
import '../services/question_service.dart';
import '../widgets/image_selector.dart';
import '../providers/question_provider.dart';

/// Helper function to get display name for question types
String getTypeDisplayName(QuestionType? type) {
  if (type == null) return 'MCQ';
  switch (type) {
    case QuestionType.mcq:
      return 'MCQ';
    case QuestionType.flipCard:
      return 'FLIP CARD';
  }
}

/// Professional screen for adding/editing questions with modern design
class AddQuestionScreen extends ConsumerStatefulWidget {
  final String? questionId; // null for add, non-null for edit
  final String courseId;
  final String yearId;
  final String subjectId;
  final String type;

  const AddQuestionScreen({
    super.key,
    this.questionId,
    required this.courseId,
    required this.yearId,
    required this.subjectId,
    required this.type,
  });

  @override
  ConsumerState<AddQuestionScreen> createState() => _AddQuestionScreenState();
}

class _AddQuestionScreenState extends ConsumerState<AddQuestionScreen> {
  final _questionController = TextEditingController();
  final _answerController = TextEditingController();
  final _explanationController = TextEditingController();
  final _topicController = TextEditingController();
  final List<TextEditingController> _optionControllers = [];
  final Map<String, QuestionImageData> _images = {};

  int _correctOption = 0;
  bool _isLoading = false;

  // Generate storage paths once to prevent refresh issues
  late final String _baseStoragePath;
  late final String _questionStoragePath;
  late final String _answerStoragePath;
  late final String _explanationStoragePath;

  QuestionType get _questionType {
    return widget.type == 'mcq' ? QuestionType.mcq : QuestionType.flipCard;
  }

  bool get _isEditMode => widget.questionId != null;

  @override
  void initState() {
    super.initState();

    // Initialize storage paths once
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    _baseStoragePath = 'questions/${_isEditMode ? widget.questionId : timestamp}';
    _questionStoragePath = '$_baseStoragePath/question';
    _answerStoragePath = '$_baseStoragePath/answer';
    _explanationStoragePath = '$_baseStoragePath/explanation';

    if (_questionType == QuestionType.mcq) {
      for (int i = 0; i < 4; i++) {
        _optionControllers.add(TextEditingController());
      }
    }

    // Load existing question data if in edit mode
    if (_isEditMode) {
      _loadQuestionData();
    } else {
      // Pre-fill topic from current filter for new questions
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final currentFilter = ref.read(filterProvider);
        if (currentFilter.topic != null && currentFilter.topic!.isNotEmpty) {
          setState(() {
            _topicController.text = currentFilter.topic!;
          });
        }
      });
    }
  }

  void _loadQuestionData() async {
    try {
      final question = await ref.read(getQuestionProvider(widget.questionId!).future);
      if (question != null && mounted) {
        setState(() {
          _questionController.text = question.question;
          _explanationController.text = question.explanation ?? '';
          _topicController.text = question.topic ?? '';

          if (question.type == QuestionType.mcq) {
            // Clear existing controllers and add new ones for the question's options
            for (var controller in _optionControllers) {
              controller.dispose();
            }
            _optionControllers.clear();

            for (int i = 0; i < question.options.length; i++) {
              _optionControllers.add(TextEditingController(text: question.options[i]));
            }
            _correctOption = question.correctOptionIndex;
          } else if (question.type == QuestionType.flipCard) {
            _answerController.text = question.answer ?? '';
          }

          // TODO: Load existing images if needed
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading question: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _questionController.dispose();
    _answerController.dispose();
    _explanationController.dispose();
    _topicController.dispose();
    for (var controller in _optionControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text('${_isEditMode ? 'Edit' : 'Add New'} ${getTypeDisplayName(_questionType)} Question'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.grey.shade800,
        elevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          TextButton(
            onPressed: () => context.pop(),
            child: const Text('Cancel'),
          ),
          const SizedBox(width: 8),
          AppButton(
            label: _isEditMode ? 'Update Question' : 'Save Question',
            onPressed: _isLoading ? null : _handleSave,
            isLoading: _isLoading,
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(32),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Question Section
            _buildSimpleQuestionSection(),
            const SizedBox(height: 24),

            // Type-specific sections
            if (_questionType == QuestionType.mcq) ...[
              _buildSimpleMCQSection(),
              const SizedBox(height: 24),
            ] else if (_questionType == QuestionType.flipCard) ...[
              _buildSimpleAnswerSection(),
              const SizedBox(height: 24),
            ],

            // Topic section
            _buildTopicSection(),
            const SizedBox(height: 24),

            // Explanation section
            _buildSimpleExplanationSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleQuestionSection() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Question Text
        Expanded(
          flex: 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Question',
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _questionController,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  hintText: 'Enter your question here...',
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),

        // Question Image
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Image',
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 88, // Match the height of the text field
                child: ImageSelector(
                  storagePath: _questionStoragePath,
                  onImageSelected: (bytes, fileName) {
                    if (bytes != null && fileName != null) {
                      _images['question'] = QuestionImageData(
                        bytes: bytes,
                        fileName: fileName,
                        storagePath: _questionStoragePath,
                      );
                    }
                  },
                  onDeleteImage: () => _images.remove('question'),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSimpleMCQSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < _optionControllers.length; i++) ...[
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Option Text
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Radio<int>(
                          value: i,
                          groupValue: _correctOption,
                          onChanged: (value) => setState(() => _correctOption = value!),
                        ),
                        Text(
                          'Option ${String.fromCharCode(65 + i)}',
                          style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
                        ),
                        const Spacer(),
                        if (_optionControllers.length > 2)
                          IconButton(
                            onPressed: () => _removeOption(i),
                            icon: const Icon(Icons.remove_circle, color: Colors.red),
                          ),
                      ],
                    ),
                    TextField(
                      controller: _optionControllers[i],
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'Enter option text...',
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),

              // Option Image
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8), // Align with option label
                    const Text(
                      'Image',
                      style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 56, // Match the height of the text field
                      child: ImageSelector(
                        storagePath: '$_baseStoragePath/option_$i',
                        onImageSelected: (bytes, fileName) {
                          if (bytes != null && fileName != null) {
                            _images['option_$i'] = QuestionImageData(
                              bytes: bytes,
                              fileName: fileName,
                              storagePath: '$_baseStoragePath/option_$i',
                            );
                          }
                        },
                        onDeleteImage: () => _images.remove('option_$i'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],

        TextButton.icon(
          onPressed: _addOption,
          icon: const Icon(Icons.add),
          label: const Text('Add Another Option'),
        ),
      ],
    );
  }

  Widget _buildSimpleAnswerSection() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Answer Text
        Expanded(
          flex: 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Answer',
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _answerController,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  hintText: 'Enter the answer here...',
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),

        // Answer Image
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Image',
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 88, // Match the height of the text field
                child: ImageSelector(
                  storagePath: _answerStoragePath,
                  onImageSelected: (bytes, fileName) {
                    if (bytes != null && fileName != null) {
                      _images['answer'] = QuestionImageData(
                        bytes: bytes,
                        fileName: fileName,
                        storagePath: _answerStoragePath,
                      );
                    }
                  },
                  onDeleteImage: () => _images.remove('answer'),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTopicSection() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Topic Dropdown/Input
        Expanded(
          flex: 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Topic',
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
              ),
              const SizedBox(height: 8),
              Consumer(
                builder: (context, ref, child) {
                  final topicsAsync = ref.watch(availableTopicsProvider);

                  return topicsAsync.when(
                    data: (topics) {
                      final currentTopic = _topicController.text.trim();

                      // Sort existing topics alphabetically
                      final sortedTopics = [...topics]..sort();

                      // If current topic is new (not in existing list), put it at the top
                      final displayTopics = <String>[];
                      if (currentTopic.isNotEmpty && !topics.contains(currentTopic)) {
                        displayTopics.add(currentTopic); // New topic at top
                      }
                      displayTopics.addAll(sortedTopics); // Then existing topics

                      return DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          hintText: 'Select topic...',
                        ),
                        value: currentTopic.isEmpty ? null : currentTopic,
                        isExpanded: true,
                        items: [
                          // No Topic
                          const DropdownMenuItem<String>(
                            value: '',
                            child: Text('No Topic'),
                          ),
                          // Add New Topic
                          const DropdownMenuItem<String>(
                            value: '__ADD_NEW__',
                            child: Row(
                              children: [
                                Icon(Icons.add, size: 16, color: Colors.blue),
                                SizedBox(width: 8),
                                Text('Add New Topic...', style: TextStyle(color: Colors.blue)),
                              ],
                            ),
                          ),
                          // All topics (new ones at top, then existing)
                          ...displayTopics.map((topic) {
                            final isNewTopic = !topics.contains(topic);
                            return DropdownMenuItem<String>(
                              value: topic,
                              child: Row(
                                children: [
                                  if (isNewTopic) ...[
                                    const Icon(Icons.fiber_new, size: 16, color: Colors.green),
                                    const SizedBox(width: 4),
                                  ],
                                  Expanded(
                                    child: Text(
                                      topic,
                                      style: TextStyle(
                                        fontWeight: isNewTopic ? FontWeight.bold : FontWeight.normal,
                                        color: isNewTopic ? Colors.green : Colors.black,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }),
                        ],
                        onChanged: (value) async {
                          if (value == '__ADD_NEW__') {
                            final newTopic = await _showAddTopicDialog(context);
                            if (newTopic != null && newTopic.isNotEmpty) {
                              setState(() {
                                _topicController.text = newTopic;
                              });
                            }
                          } else {
                            setState(() {
                              _topicController.text = value ?? '';
                            });
                          }
                        },
                      );
                    },
                    loading: () => DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'Loading topics...',
                        prefixIcon: Icon(Icons.topic),
                      ),
                      items: const [],
                      onChanged: null,
                    ),
                    error: (error, stack) => DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'Select topic...',
                      ),
                      items: const [
                        DropdownMenuItem<String>(
                          value: '__ADD_NEW__',
                          child: Row(
                            children: [
                              Icon(Icons.add, size: 16, color: Colors.blue),
                              SizedBox(width: 8),
                              Text('Add New Topic...', style: TextStyle(color: Colors.blue)),
                            ],
                          ),
                        ),
                      ],
                      onChanged: (value) async {
                        if (value == '__ADD_NEW__') {
                          final newTopic = await _showAddTopicDialog(context);
                          if (newTopic != null && newTopic.isNotEmpty) {
                            setState(() {
                              _topicController.text = newTopic;
                            });
                          }
                        }
                      },
                    ),
                  );
                },
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),

        // Empty space to align with other sections
        Expanded(
          flex: 2,
          child: Container(), // Empty container to maintain layout consistency
        ),
      ],
    );
  }

  Widget _buildSimpleExplanationSection() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Explanation Text
        Expanded(
          flex: 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Explanation',
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _explanationController,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  hintText: 'Enter explanation here...',
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),

        // Explanation Image
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Image',
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 88, // Match the height of the text field
                child: ImageSelector(
                  storagePath: _explanationStoragePath,
                  onImageSelected: (bytes, fileName) {
                    if (bytes != null && fileName != null) {
                      _images['explanation'] = QuestionImageData(
                        bytes: bytes,
                        fileName: fileName,
                        storagePath: _explanationStoragePath,
                      );
                    }
                  },
                  onDeleteImage: () => _images.remove('explanation'),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _addOption() {
    if (_optionControllers.length < 10) {
      setState(() {
        _optionControllers.add(TextEditingController());
      });
    }
  }

  void _removeOption(int index) {
    if (_optionControllers.length > 2) {
      setState(() {
        _optionControllers[index].dispose();
        _optionControllers.removeAt(index);
        if (_correctOption >= index && _correctOption > 0) {
          _correctOption--;
        }
      });
    }
  }

  void _handleSave() async {
    // Simple validation
    if (_questionController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter question text')),
      );
      return;
    }

    if (_questionType == QuestionType.mcq) {
      int filledOptions = 0;
      for (var controller in _optionControllers) {
        if (controller.text.trim().isNotEmpty) {
          filledOptions++;
        }
      }
      if (filledOptions < 2) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please fill at least 2 options')),
        );
        return;
      }
    } else if (_questionType == QuestionType.flipCard) {
      if (_answerController.text.trim().isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please enter answer text')),
        );
        return;
      }
    }

    setState(() => _isLoading = true);

    try {
      // Use existing ID for edit mode, or create new ID for add mode
      final questionId = _isEditMode ? widget.questionId! : 'q_${DateTime.now().millisecondsSinceEpoch}';
      // Get current filter to use its difficulty, or default to easy
      final currentFilter = ref.read(filterProvider);
      final questionDifficulty = currentFilter.difficulty ?? TestDifficulty.easy;

      final question = QuestionEntity(
        id: questionId,
        question: _questionController.text.trim(),
        type: _questionType,
        courseId: widget.courseId,
        yearId: widget.yearId,
        subjectId: widget.subjectId,
        difficulty: questionDifficulty,
        topic: _topicController.text.trim().isEmpty ? null : _topicController.text.trim(),
        options: _questionType == QuestionType.mcq
            ? _optionControllers.map((c) => c.text.trim()).toList()
            : [],
        correctOptionIndex: _questionType == QuestionType.mcq ? _correctOption : 0,
        answer: _questionType == QuestionType.flipCard ? _answerController.text.trim() : null,
        explanation: _explanationController.text.trim().isEmpty ? null : _explanationController.text.trim(),
        createdAt: _isEditMode ? null : DateTime.now(), // Keep original creation date in edit mode
        updatedAt: DateTime.now(),
      );

      // Save question with images using provider
      await ref.read(saveQuestionProvider(
        questionId: questionId,
        question: question,
        imageMap: _images,
        deleteImages: <String, String>{},
        isEditMode: _isEditMode,
      ).future);

      // Add topic to subject if it's a new topic
      if (question.topic != null && question.topic!.isNotEmpty) {
        try {
          await TopicService.addTopicToSubject(
            courseId: widget.courseId,
            yearId: widget.yearId,
            subjectId: widget.subjectId,
            topicName: question.topic!,
          );

          // Wait a bit for Firestore to propagate changes
          await Future.delayed(const Duration(milliseconds: 500));

          // Force refresh the root coursesProvider and invalidate dependent providers
          try {
            final _ = await ref.refresh(coursesProvider.future);
            ref.invalidate(availableCoursesProvider);
            ref.invalidate(availableSubjectsProvider);
            ref.invalidate(availableTopicsProvider);
          } catch (e) {
            debugPrint('Error refreshing providers: $e');
          }

        } catch (e) {
          // Log error but don't fail the question creation
          debugPrint('Error adding topic to subject: $e');
        }
      }

      // Invalidate the questions query to refresh the questions list
      ref.invalidate(filteredQuestionsQueryProvider(
        courseId: widget.courseId,
        yearId: widget.yearId,
        subjectId: widget.subjectId,
      ));

      if (mounted) {
        // Return the created/updated question to the previous screen
        context.pop(question);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(_isEditMode ? 'Question updated successfully!' : 'Question created successfully!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving question: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<String?> _showAddTopicDialog(BuildContext context) async {
    final controller = TextEditingController();

    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Topic'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Enter the name for the new topic:'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Topic name...',
              ),
              autofocus: true,
              onSubmitted: (value) {
                if (value.trim().isNotEmpty) {
                  Navigator.of(context).pop(value.trim());
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final topic = controller.text.trim();
              if (topic.isNotEmpty) {
                Navigator.of(context).pop(topic);
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }






}
