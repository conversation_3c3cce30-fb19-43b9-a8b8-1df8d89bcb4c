﻿
import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_provider.g.dart';

class AdminUser {
  final User firebaseUser;

  const AdminUser({
    required this.firebaseUser,
  });

  String get uid => firebaseUser.uid;
  String? get email => firebaseUser.email;
  String? get displayName => firebaseUser.displayName;
  bool get emailVerified => firebaseUser.emailVerified;
  String get roleDisplayName => 'Admin';
}

@riverpod
Stream<User?> authStateChanges(Ref ref) {
  return FirebaseAuth.instance.authStateChanges();
}

@riverpod
Stream<AdminUser?> authState(Ref ref) async* {
  await for (final user in FirebaseAuth.instance.authStateChanges()) {
    if (user == null) {
      yield null;
    } else {
      final role = await _getAdminRoleFromFirestore(user.uid);

      if (role != 'admin') {
        throw Exception('Access denied. Admin privileges required.');
      }

      yield AdminUser(firebaseUser: user);
    }
  }
}

@riverpod
class AuthActions extends _$AuthActions {
  @override
  FutureOr<void> build() {}

  Future<void> signIn({required String email, required String password}) async {
    state = const AsyncLoading();

    state = await AsyncValue.guard(() async {
      final trimmedEmail = email.trim();
      if (trimmedEmail.isEmpty) {
        throw Exception('Email is required');
      }
      if (password.isEmpty) {
        throw Exception('Password is required');
      }

      final credential = await FirebaseAuth.instance.signInWithEmailAndPassword(
        email: trimmedEmail,
        password: password,
      );

      if (credential.user == null) {
        throw Exception('Login failed. Please try again.');
      }

      ref.invalidate(authStateProvider);
    });
  }

  Future<void> resetPassword({required String email}) async {
    state = const AsyncLoading();

    state = await AsyncValue.guard(() async {
      final trimmedEmail = email.trim();
      if (trimmedEmail.isEmpty) {
        throw Exception('Email is required');
      }

      await FirebaseAuth.instance.sendPasswordResetEmail(
        email: trimmedEmail,
      );
    });
  }

  Future<void> signOut() async {
    state = const AsyncLoading();

    try {
      await FirebaseAuth.instance.signOut();
    } catch (error) {
      state = AsyncError(error, StackTrace.current);
    }
  }
}

@riverpod
bool isAuthenticated(Ref ref) {
  final authState = ref.watch(authStateProvider);
  return authState.hasValue && authState.value != null;
}

@riverpod
AdminUser? currentAdminUser(Ref ref) {
  final authState = ref.watch(authStateProvider);
  return authState.valueOrNull;
}

Future<String?> _getAdminRoleFromFirestore(String uid) async {
  final claimsDoc = await FirebaseFirestore.instance
      .collection('user_claims')
      .doc(uid)
      .get();

  if (!claimsDoc.exists) {
    return null;
  }

  final claimsData = claimsDoc.data();
  return claimsData?['role'] as String?;
}

String getUserFriendlyAuthError(dynamic error) {
  if (error == null) return 'An unexpected error occurred';

  final errorString = error.toString().toLowerCase();

  if (errorString.contains('user-not-found')) {
    return 'No admin account found with this email address';
  } else if (errorString.contains('wrong-password') ||
      errorString.contains('invalid-credential')) {
    return 'Invalid email or password. Please check your credentials';
  } else if (errorString.contains('invalid-email')) {
    return 'Please enter a valid email address';
  } else if (errorString.contains('too-many-requests')) {
    return 'Too many failed attempts. Please wait a few minutes before trying again';
  } else if (errorString.contains('network-request-failed') ||
      errorString.contains('network')) {
    return 'Network error. Please check your internet connection and try again';
  } else if (errorString.contains('user-disabled')) {
    return 'This account has been disabled. Please contact support';
  } else if (errorString.contains('operation-not-allowed')) {
    return 'Email/password sign-in is not enabled. Please contact support';
  } else if (errorString.contains('weak-password')) {
    return 'Password is too weak. Please choose a stronger password';
  } else if (errorString.contains('email-already-in-use')) {
    return 'An account with this email already exists';
  } else if (errorString.contains('requires-recent-login')) {
    return 'Please sign in again to continue';
  } else if (errorString.contains('cancelled')) {
    return 'Sign-in was cancelled. Please try again';
  } else if (errorString.contains('access denied') && errorString.contains('admin privileges required')) {
    return 'Access denied. This account does not have admin privileges. If you just created this account, please wait a moment and try again';
  }

  if (errorString.startsWith('exception: ')) {
    return errorString.replaceFirst('exception: ', '');
  }

  return 'Authentication failed. Please try again or contact support if the problem persists';
}
