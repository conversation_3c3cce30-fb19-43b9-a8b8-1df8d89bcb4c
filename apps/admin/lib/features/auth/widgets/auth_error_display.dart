﻿import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/auth_provider.dart';

class AuthErrorDisplay extends ConsumerWidget {
  const AuthErrorDisplay({super.key});
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authActions = ref.watch(authActionsProvider);
    
    if (!authActions.hasError) {
      return const SizedBox.shrink();
    }
    
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.red.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red.shade200),
          ),
          child: Row(
            children: [
              Icon(Icons.error, color: Colors.red.shade600),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  getUserFriendlyAuthError(authActions.error),
                  style: TextStyle(color: Colors.red.shade800),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }
}
