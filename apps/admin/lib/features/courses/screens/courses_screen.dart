// apps/admin/lib/features/courses/screens/courses_screen.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../shared/widgets/base_content_screen.dart';
import '../providers/course_provider.dart';
import '../widgets/course_table.dart';

class CoursesScreen extends ConsumerWidget {
  const CoursesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final coursesAsync = ref.watch(courseProvider);

    final totalCount = coursesAsync.when(
      data: (courses) => courses.courses.length.toString(),
      loading: () => '...',
      error: (_, __) => '?',
    );

    return BaseContentScreen(
      title: 'Courses',
      totalCount: totalCount,
      addButtonText: 'Add Course',
      onAdd: () => context.push("/add-course"),
      dataTable: const CourseTable(),
      showFilterBar: false,
    );
  }
}
