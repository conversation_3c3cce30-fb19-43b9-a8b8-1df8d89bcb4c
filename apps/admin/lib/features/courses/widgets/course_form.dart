﻿
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../shared/widgets/tag_input_field.dart';
import 'package:entities/entities.dart';

class CourseFormData {
  final String courseName;
  final List<String> years;
  final Map<String, List<String>> subjectsByYear;
  final Map<String, List<String>> sectionsBySubject;
  final Map<String, Map<String, List<String>>> topicsBySection;

  CourseFormData({
    this.courseName = '',
    this.years = const [],
    this.subjectsByYear = const {},
    this.sectionsBySubject = const {},
    this.topicsBySection = const {},
  });

  CourseFormData copyWith({
    String? courseName,
    List<String>? years,
    Map<String, List<String>>? subjectsByYear,
    Map<String, List<String>>? sectionsBySubject,
    Map<String, Map<String, List<String>>>? topicsBySection,
  }) {
    return CourseFormData(
      courseName: courseName ?? this.courseName,
      years: years ?? this.years,
      subjectsByYear: subjectsByYear ?? this.subjectsByYear,
      sectionsBySubject: sectionsBySubject ?? this.sectionsBySubject,
      topicsBySection: topicsBySection ?? this.topicsBySection,
    );
  }

  CourseEntity toCourseEntity() {
    final yearsMap = <String, YearEntity>{};
    
    for (final year in years) {
      final subjects = subjectsByYear[year] ?? [];
      final subjectsMap = <String, SubjectEntity>{};
      
      for (final subject in subjects) {
        final sections = sectionsBySubject[subject] ?? [];
        final sectionsMap = <String, SectionEntity>{};
        
        for (final section in sections) {
          final topics = topicsBySection[subject]?[section] ?? [];
          final topicsMap = <String, TopicEntity>{};
          
          for (final topic in topics) {
            topicsMap[topic] = TopicEntity();
          }
          
          sectionsMap[section] = SectionEntity(topics: topicsMap);
        }
        
        subjectsMap[subject] = SubjectEntity(sections: sectionsMap);
      }
      
      yearsMap[year] = YearEntity(subjects: subjectsMap);
    }
    
    return CourseEntity(years: yearsMap);
  }

  static CourseFormData fromCourseEntity(String courseName, CourseEntity course) {
    final years = course.years.keys.toList();
    final subjectsByYear = <String, List<String>>{};
    final sectionsBySubject = <String, List<String>>{};
    final topicsBySection = <String, Map<String, List<String>>>{};

    for (final yearEntry in course.years.entries) {
      final yearName = yearEntry.key;
      final year = yearEntry.value;

      subjectsByYear[yearName] = year.subjects.keys.toList();

      for (final subjectEntry in year.subjects.entries) {
        final subjectName = subjectEntry.key;
        final subject = subjectEntry.value;

        sectionsBySubject[subjectName] = subject.sections.keys.toList();

        topicsBySection[subjectName] = {
          for (final sectionEntry in subject.sections.entries)
            sectionEntry.key: sectionEntry.value.topics.keys.toList()
        };
      }
    }

    return CourseFormData(
      courseName: courseName,
      years: years,
      subjectsByYear: subjectsByYear,
      sectionsBySubject: sectionsBySubject,
      topicsBySection: topicsBySection,
    );
  }

  String? getValidationError() {
    if (courseName.trim().isEmpty) {
      return 'Course name is required';
    }
    if (years.isEmpty) {
      return 'At least one year is required';
    }
    for (final year in years) {
      final subjects = subjectsByYear[year] ?? [];
      if (subjects.isEmpty) {
        return 'Year "$year" must have at least one subject';
      }
    }
    return null;
  }
}

class CourseForm extends ConsumerStatefulWidget {
  final CourseFormData initialData;
  final ValueChanged<CourseFormData> onDataChanged;

  const CourseForm({
    super.key,
    required this.initialData,
    required this.onDataChanged,
  });

  @override
  ConsumerState<CourseForm> createState() => _CourseFormState();
}

class _CourseFormState extends ConsumerState<CourseForm> {
  late CourseFormData _formData;
  final _courseNameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _formData = widget.initialData;
    _courseNameController.text = _formData.courseName;
  }

  @override
  void dispose() {
    _courseNameController.dispose();
    super.dispose();
  }

  void _updateFormData(CourseFormData newData) {
    setState(() => _formData = newData);
    widget.onDataChanged(newData);
  }

  void _updateCourseName(String name) {
    _updateFormData(_formData.copyWith(courseName: name));
  }

  void _updateYears(List<String> years) {
    final newSubjectsByYear = Map<String, List<String>>.from(_formData.subjectsByYear);
    newSubjectsByYear.removeWhere((year, _) => !years.contains(year));
    
    _updateFormData(_formData.copyWith(
      years: years,
      subjectsByYear: newSubjectsByYear,
    ));
  }

  void _updateSubjects(String year, List<String> subjects) {
    final newSubjectsByYear = Map<String, List<String>>.from(_formData.subjectsByYear);
    newSubjectsByYear[year] = subjects;

    final newSectionsBySubject = Map<String, List<String>>.from(_formData.sectionsBySubject);
    newSectionsBySubject.removeWhere((subject, _) => !subjects.contains(subject));

    _updateFormData(_formData.copyWith(
      subjectsByYear: newSubjectsByYear,
      sectionsBySubject: newSectionsBySubject,
    ));
  }

  void _updateSections(String subject, List<String> sections) {
    final newSectionsBySubject = Map<String, List<String>>.from(_formData.sectionsBySubject);

    if (sections.isEmpty) {
      newSectionsBySubject.remove(subject);
    } else {
      newSectionsBySubject[subject] = sections;
    }

    _updateFormData(_formData.copyWith(sectionsBySubject: newSectionsBySubject));
  }

  void _updateTopics(String subject, String section, List<String> topics) {
    final newTopicsBySection = Map<String, Map<String, List<String>>>.from(_formData.topicsBySection);

    if (!newTopicsBySection.containsKey(subject)) {
      newTopicsBySection[subject] = {};
    }

    newTopicsBySection[subject]![section] = topics;

    _updateFormData(_formData.copyWith(topicsBySection: newTopicsBySection));
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 400,
            child: TextField(
              controller: _courseNameController,
              decoration: const InputDecoration(
                labelText: 'Course Name *',
                hintText: 'e.g., MBBS, BDS, etc.',
                border: OutlineInputBorder(),
              ),
              onChanged: _updateCourseName,
            ),
          ),
          
          const SizedBox(height: 32),
          
          TagInputField(
            label: 'Years *',
            tags: _formData.years,
            onTagsChanged: _updateYears,
            hintText: 'e.g., 1st Year, 2nd Year',
            validator: (value) {
              if (value.isEmpty) return 'Year name cannot be empty';
              return null;
            },
          ),
          
          const SizedBox(height: 32),
          
          if (_formData.years.isNotEmpty) ...[
            Text(
              'Subjects by Year',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16),
            
            ..._formData.years.map((year) => ExpandableTagSection(
              title: '$year Subjects',
              child: TagInputField(
                label: 'Subjects for $year',
                tags: _formData.subjectsByYear[year] ?? [],
                onTagsChanged: (subjects) => _updateSubjects(year, subjects),
                hintText: 'e.g., Human Anatomy, Physiology',
              ),
            )),
          ],
          
          const SizedBox(height: 32),
          
          if (_formData.subjectsByYear.values.any((subjects) => subjects.isNotEmpty)) ...[
            Text(
              'Sections & Topics (Optional)',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Add sections and topics to organize your subjects better. This is optional and can be done later.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 16),
            
            ..._getAllSubjects().map((subject) => _buildSubjectSections(subject)),
          ],
        ],
      ),
    );
  }

  List<String> _getAllSubjects() {
    final allSubjects = <String>{};
    for (final subjects in _formData.subjectsByYear.values) {
      allSubjects.addAll(subjects);
    }
    return allSubjects.toList()..sort();
  }

  Widget _buildSubjectSections(String subject) {
    final sections = _formData.sectionsBySubject[subject] ?? [];
    
    return ExpandableTagSection(
      title: subject,
      initiallyExpanded: false,
      child: Column(
        children: [
          TagInputField(
            label: 'Sections',
            tags: sections,
            onTagsChanged: (newSections) => _updateSections(subject, newSections),
            hintText: 'e.g., General, Systems, Applied',
          ),
          
          const SizedBox(height: 16),
          
          if (sections.isNotEmpty) ...[
            Text(
              'Topics by Section',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 12),
            
            ...sections.map((section) => Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: TagInputField(
                label: '$section Topics',
                tags: _formData.topicsBySection[subject]?[section] ?? [],
                onTagsChanged: (topics) => _updateTopics(subject, section, topics),
                hintText: 'e.g., Bones, Muscles, Nerves',
              ),
            )),
          ],
        ],
      ),
    );
  }
}
