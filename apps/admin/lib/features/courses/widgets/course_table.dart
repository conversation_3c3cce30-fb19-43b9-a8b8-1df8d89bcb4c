﻿
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../shared/widgets/status_messages.dart';
import '../providers/course_provider.dart';
import 'course_actions.dart';

class CourseTable extends ConsumerStatefulWidget {
  const CourseTable({super.key});

  @override
  ConsumerState<CourseTable> createState() => _CourseTableState();
}

class _CourseTableState extends ConsumerState<CourseTable> {
  final _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final coursesAsync = ref.watch(courseProvider);

    return coursesAsync.when(
      loading: () => const LoadingMessage(message: "Loading courses..."),
      error: (error, stack) => ErrorMessage(
        title: "Error Loading Courses",
        message: "There was a problem retrieving the courses.",
        onRetry: () => ref.invalidate(courseProvider),
      ),
      data: (coursesEntity) {
        if (coursesEntity.courses.isEmpty) {
          return const EmptyDataMessage(
            title: "No Courses Found",
            message: "No courses have been configured yet. Add a new course to get started.",
          );
        }

        final courseEntries = coursesEntity.courses.entries.toList();
        final filteredCourseEntries = courseEntries.where((entry) {
          return entry.key.toLowerCase().contains(_searchQuery.toLowerCase());
        }).toList();

        return Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      decoration: const InputDecoration(
                        labelText: "Search courses",
                        hintText: "Search by course name...",
                        prefixIcon: Icon(Icons.search),
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                child: SizedBox(
                  width: double.infinity,
                  child: Theme(
                    data: Theme.of(context).copyWith(
                      dataTableTheme: DataTableTheme.of(context).copyWith(
                        headingRowColor: WidgetStateProperty.all(const Color(0xFFE6E9FF)),
                        dataRowMinHeight: 60,
                        dataRowMaxHeight: 60,
                        headingRowHeight: 48,
                        columnSpacing: 16,
                      ),
                    ),
                    child: DataTable(
                      columns: const [
                        DataColumn(label: Text('Course')),
                        DataColumn(label: Text('Years')),
                        DataColumn(label: Text('Subjects')),
                        DataColumn(label: Text('Actions')),
                      ],
                      rows: filteredCourseEntries.map((entry) {
                        final courseId = entry.key;
                        final course = entry.value;
                        final totalSubjects = course.years.values
                            .map((year) => year.subjects.length)
                            .fold(0, (sum, count) => sum + count);

                        return DataRow(
                          onSelectChanged: (_) {
                            context.push('/view-course/$courseId');
                          },
                          cells: [
                            DataCell(
                              Text(
                                courseId,
                                style: const TextStyle(fontWeight: FontWeight.w500),
                              ),
                            ),
                            DataCell(
                              Text('${course.years.length}'),
                            ),
                            DataCell(
                              Text('$totalSubjects'),
                            ),
                            DataCell(
                              CourseActionMenu(courseId: courseId, course: course),
                            ),
                          ],
                        );
                      }).toList(),
                      horizontalMargin: 16,
                      showCheckboxColumn: false,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }


}
