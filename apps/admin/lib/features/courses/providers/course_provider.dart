﻿

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/courses_entity.dart';
import 'package:entities/course_entity.dart';
import 'package:entities/year_entity.dart';
import 'package:entities/subject_entity.dart';
import 'package:entities/section_entity.dart';
import 'package:entities/topic_entity.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'course_provider.g.dart';


@riverpod
class Course extends _$Course {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _courseCatalogCollection = 'course_catalog';
  static const String _structureDocument = 'structure';

  @override
  Future<CoursesEntity> build() async {
    return await _getCourses();
  }


  Future<CoursesEntity> _getCourses() async {
    final snapshot = await _firestore
        .collection(_courseCatalogCollection)
        .doc(_structureDocument)
        .get();

    if (!snapshot.exists || snapshot.data() == null) {
      return CoursesEntity();
    }

    return CoursesEntity.fromJson(snapshot.data()!);
  }


  Future<void> _updateCourseStructure(CoursesEntity coursesEntity) async {
    final coursesRef =
        _firestore.collection(_courseCatalogCollection).doc(_structureDocument);
    await coursesRef.set(coursesEntity.toJson());
  }




  Future<void> createCourse(
      String courseName, CourseEntity courseEntity) async {
    final currentState = state.valueOrNull ?? CoursesEntity();
    final optimisticCourses =
        Map<String, CourseEntity>.from(currentState.courses);
    optimisticCourses[courseName] = courseEntity;


    state = AsyncData(currentState.copyWith(courses: optimisticCourses));


    final result = await AsyncValue.guard(() async {
      final coursesEntity = await _getCourses();

      if (coursesEntity.courses.containsKey(courseName)) {
        throw Exception('Course "$courseName" already exists');
      }

      final updatedCourses =
          Map<String, CourseEntity>.from(coursesEntity.courses);
      updatedCourses[courseName] = courseEntity;

      final updatedCoursesEntity =
          coursesEntity.copyWith(courses: updatedCourses);
      await _updateCourseStructure(updatedCoursesEntity);

      ref.invalidateSelf();
    });


    if (result.hasError) {
      state = AsyncData(currentState);
    }
  }

  Future<void> deleteCourse(String courseName) async {
    final currentState = state.valueOrNull ?? CoursesEntity();
    final optimisticCourses =
        Map<String, CourseEntity>.from(currentState.courses);
    optimisticCourses.remove(courseName);

    state = AsyncData(currentState.copyWith(courses: optimisticCourses));

    final result = await AsyncValue.guard(() async {
      final coursesEntity = await _getCourses();

      if (!coursesEntity.courses.containsKey(courseName)) {
        throw Exception('Course "$courseName" does not exist');
      }

      final updatedCourses =
          Map<String, CourseEntity>.from(coursesEntity.courses);
      updatedCourses.remove(courseName);

      final updatedCoursesEntity =
          coursesEntity.copyWith(courses: updatedCourses);
      await _updateCourseStructure(updatedCoursesEntity);

      ref.invalidateSelf();
    });

    if (result.hasError) {
      state = AsyncData(currentState);
    }
  }

  Future<void> updateCourse(
      String courseName, CourseEntity updatedCourse) async {
    final currentState = state.valueOrNull ?? CoursesEntity();
    final optimisticCourses =
        Map<String, CourseEntity>.from(currentState.courses);
    optimisticCourses[courseName] = updatedCourse;

    state = AsyncData(currentState.copyWith(courses: optimisticCourses));

    final result = await AsyncValue.guard(() async {
      final coursesEntity = await _getCourses();

      if (!coursesEntity.courses.containsKey(courseName)) {
        throw Exception('Course "$courseName" does not exist');
      }

      final updatedCourses =
          Map<String, CourseEntity>.from(coursesEntity.courses);
      updatedCourses[courseName] = updatedCourse;

      final updatedCoursesEntity =
          coursesEntity.copyWith(courses: updatedCourses);
      await _updateCourseStructure(updatedCoursesEntity);

      ref.invalidateSelf();
    });

    if (result.hasError) {
      state = AsyncData(currentState);
    }
  }


  Future<void> addYear(String courseName, String yearName) async {
    await AsyncValue.guard(() async {
      final coursesEntity = await _getCourses();

      final course = coursesEntity.courses[courseName];
      if (course == null) {
        throw Exception('Course "$courseName" does not exist');
      }

      if (course.years.containsKey(yearName)) {
        throw Exception(
            'Year "$yearName" already exists in course "$courseName"');
      }

      final updatedYears = Map<String, YearEntity>.from(course.years);
      updatedYears[yearName] = YearEntity();

      final updatedCourse = course.copyWith(years: updatedYears);
      final updatedCourses =
          Map<String, CourseEntity>.from(coursesEntity.courses);
      updatedCourses[courseName] = updatedCourse;

      final updatedCoursesEntity =
          coursesEntity.copyWith(courses: updatedCourses);
      await _updateCourseStructure(updatedCoursesEntity);

      ref.invalidateSelf();
    });
  }

  Future<void> removeYear(String courseName, String yearName) async {
    await AsyncValue.guard(() async {
      final coursesEntity = await _getCourses();

      final course = coursesEntity.courses[courseName];
      if (course == null) {
        throw Exception('Course "$courseName" does not exist');
      }

      if (!course.years.containsKey(yearName)) {
        throw Exception(
            'Year "$yearName" does not exist in course "$courseName"');
      }

      final updatedYears = Map<String, YearEntity>.from(course.years);
      updatedYears.remove(yearName);

      final updatedCourse = course.copyWith(years: updatedYears);
      final updatedCourses =
          Map<String, CourseEntity>.from(coursesEntity.courses);
      updatedCourses[courseName] = updatedCourse;

      final updatedCoursesEntity =
          coursesEntity.copyWith(courses: updatedCourses);
      await _updateCourseStructure(updatedCoursesEntity);

      ref.invalidateSelf();
    });
  }


  Future<void> addSubject(
      String courseName, String yearName, String subjectName) async {
    await AsyncValue.guard(() async {
      final coursesEntity = await _getCourses();

      final course = coursesEntity.courses[courseName];
      if (course == null) {
        throw Exception('Course "$courseName" does not exist');
      }

      final year = course.years[yearName];
      if (year == null) {
        throw Exception(
            'Year "$yearName" does not exist in course "$courseName"');
      }

      if (year.subjects.containsKey(subjectName)) {
        throw Exception(
            'Subject "$subjectName" already exists in year "$yearName"');
      }

      final updatedSubjects = Map<String, SubjectEntity>.from(year.subjects);
      updatedSubjects[subjectName] = SubjectEntity();

      final updatedYear = year.copyWith(subjects: updatedSubjects);
      final updatedYears = Map<String, YearEntity>.from(course.years);
      updatedYears[yearName] = updatedYear;

      final updatedCourse = course.copyWith(years: updatedYears);
      final updatedCourses =
          Map<String, CourseEntity>.from(coursesEntity.courses);
      updatedCourses[courseName] = updatedCourse;

      final updatedCoursesEntity =
          coursesEntity.copyWith(courses: updatedCourses);
      await _updateCourseStructure(updatedCoursesEntity);

      ref.invalidateSelf();
    });
  }

  Future<void> removeSubject(
      String courseName, String yearName, String subjectName) async {
    await AsyncValue.guard(() async {
      final coursesEntity = await _getCourses();

      final course = coursesEntity.courses[courseName];
      if (course == null) {
        throw Exception('Course "$courseName" does not exist');
      }

      final year = course.years[yearName];
      if (year == null) {
        throw Exception(
            'Year "$yearName" does not exist in course "$courseName"');
      }

      if (!year.subjects.containsKey(subjectName)) {
        throw Exception(
            'Subject "$subjectName" does not exist in year "$yearName"');
      }

      final updatedSubjects = Map<String, SubjectEntity>.from(year.subjects);
      updatedSubjects.remove(subjectName);

      final updatedYear = year.copyWith(subjects: updatedSubjects);
      final updatedYears = Map<String, YearEntity>.from(course.years);
      updatedYears[yearName] = updatedYear;

      final updatedCourse = course.copyWith(years: updatedYears);
      final updatedCourses =
          Map<String, CourseEntity>.from(coursesEntity.courses);
      updatedCourses[courseName] = updatedCourse;

      final updatedCoursesEntity =
          coursesEntity.copyWith(courses: updatedCourses);
      await _updateCourseStructure(updatedCoursesEntity);

      ref.invalidateSelf();
    });
  }


  Future<void> addSection(String courseName, String yearName,
      String subjectName, String sectionName) async {
    await AsyncValue.guard(() async {
      final coursesEntity = await _getCourses();

      final subject =
          _getSubject(coursesEntity, courseName, yearName, subjectName);

      if (subject.sections.containsKey(sectionName)) {
        throw Exception(
            'Section "$sectionName" already exists in subject "$subjectName"');
      }

      final updatedSections = Map<String, SectionEntity>.from(subject.sections);
      updatedSections[sectionName] = SectionEntity();

      final updatedSubject = subject.copyWith(sections: updatedSections);
      await _updateSubject(
          coursesEntity, courseName, yearName, subjectName, updatedSubject);
    });
  }

  Future<void> removeSection(String courseName, String yearName,
      String subjectName, String sectionName) async {
    await AsyncValue.guard(() async {
      final coursesEntity = await _getCourses();

      final subject =
          _getSubject(coursesEntity, courseName, yearName, subjectName);

      if (!subject.sections.containsKey(sectionName)) {
        throw Exception(
            'Section "$sectionName" does not exist in subject "$subjectName"');
      }

      final updatedSections = Map<String, SectionEntity>.from(subject.sections);
      updatedSections.remove(sectionName);

      final updatedSubject = subject.copyWith(sections: updatedSections);
      await _updateSubject(
          coursesEntity, courseName, yearName, subjectName, updatedSubject);
    });
  }


  Future<void> addTopic(String courseName, String yearName, String subjectName,
      String sectionName, String topicName) async {
    await AsyncValue.guard(() async {
      final coursesEntity = await _getCourses();

      final section = _getSection(
          coursesEntity, courseName, yearName, subjectName, sectionName);

      if (section.topics.containsKey(topicName)) {
        throw Exception(
            'Topic "$topicName" already exists in section "$sectionName"');
      }

      final updatedTopics = Map<String, TopicEntity>.from(section.topics);
      updatedTopics[topicName] = TopicEntity();

      final updatedSection = section.copyWith(topics: updatedTopics);
      await _updateSection(coursesEntity, courseName, yearName, subjectName,
          sectionName, updatedSection);
    });
  }

  Future<void> removeTopic(String courseName, String yearName,
      String subjectName, String sectionName, String topicName) async {
    await AsyncValue.guard(() async {
      final coursesEntity = await _getCourses();

      final section = _getSection(
          coursesEntity, courseName, yearName, subjectName, sectionName);

      if (!section.topics.containsKey(topicName)) {
        throw Exception(
            'Topic "$topicName" does not exist in section "$sectionName"');
      }

      final updatedTopics = Map<String, TopicEntity>.from(section.topics);
      updatedTopics.remove(topicName);

      final updatedSection = section.copyWith(topics: updatedTopics);
      await _updateSection(coursesEntity, courseName, yearName, subjectName,
          sectionName, updatedSection);
    });
  }


  SubjectEntity _getSubject(CoursesEntity coursesEntity, String courseName,
      String yearName, String subjectName) {
    final course = coursesEntity.courses[courseName];
    if (course == null) {
      throw Exception('Course "$courseName" does not exist');
    }

    final year = course.years[yearName];
    if (year == null) {
      throw Exception(
          'Year "$yearName" does not exist in course "$courseName"');
    }

    final subject = year.subjects[subjectName];
    if (subject == null) {
      throw Exception(
          'Subject "$subjectName" does not exist in year "$yearName"');
    }

    return subject;
  }

  SectionEntity _getSection(CoursesEntity coursesEntity, String courseName,
      String yearName, String subjectName, String sectionName) {
    final subject =
        _getSubject(coursesEntity, courseName, yearName, subjectName);

    final section = subject.sections[sectionName];
    if (section == null) {
      throw Exception(
          'Section "$sectionName" does not exist in subject "$subjectName"');
    }

    return section;
  }

  Future<void> _updateSubject(CoursesEntity coursesEntity, String courseName,
      String yearName, String subjectName, SubjectEntity updatedSubject) async {
    final course = coursesEntity.courses[courseName]!;
    final year = course.years[yearName]!;

    final updatedSubjects = Map<String, SubjectEntity>.from(year.subjects);
    updatedSubjects[subjectName] = updatedSubject;

    final updatedYear = year.copyWith(subjects: updatedSubjects);
    final updatedYears = Map<String, YearEntity>.from(course.years);
    updatedYears[yearName] = updatedYear;

    final updatedCourse = course.copyWith(years: updatedYears);
    final updatedCourses =
        Map<String, CourseEntity>.from(coursesEntity.courses);
    updatedCourses[courseName] = updatedCourse;

    final updatedCoursesEntity =
        coursesEntity.copyWith(courses: updatedCourses);
    await _updateCourseStructure(updatedCoursesEntity);

    ref.invalidateSelf();
  }

  Future<void> _updateSection(
      CoursesEntity coursesEntity,
      String courseName,
      String yearName,
      String subjectName,
      String sectionName,
      SectionEntity updatedSection) async {
    final subject =
        _getSubject(coursesEntity, courseName, yearName, subjectName);

    final updatedSections = Map<String, SectionEntity>.from(subject.sections);
    updatedSections[sectionName] = updatedSection;

    final updatedSubject = subject.copyWith(sections: updatedSections);
    await _updateSubject(
        coursesEntity, courseName, yearName, subjectName, updatedSubject);
  }
}
