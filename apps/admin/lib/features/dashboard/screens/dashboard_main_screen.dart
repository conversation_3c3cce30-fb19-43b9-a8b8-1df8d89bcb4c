import 'package:flutter/material.dart';

import '../widgets/dashboard_welcome.dart';
import '../widgets/dashboard_stats.dart';
import '../widgets/dashboard_actions.dart';

class DashboardMainScreen extends StatelessWidget {
  const DashboardMainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Dashboard',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: const SingleChildScrollView(
        padding: EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DashboardWelcome(),
            <PERSON><PERSON><PERSON><PERSON>(height: 32),
            DashboardStats(),
            <PERSON><PERSON><PERSON><PERSON>(height: 32),
            DashboardActions(),
          ],
        ),
      ),
    );
  }
}
