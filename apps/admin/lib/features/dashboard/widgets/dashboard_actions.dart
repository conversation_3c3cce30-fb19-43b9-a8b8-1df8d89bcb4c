import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'dashboard_action_card.dart';

class DashboardAction {
  const DashboardAction({
    required this.title,
    required this.icon,
    required this.route,
    required this.color,
  });

  final String title;
  final IconData icon;
  final String route;
  final Color color;
}

class DashboardActions extends StatelessWidget {
  const DashboardActions({super.key});

  static const List<DashboardAction> _actions = [
    DashboardAction(
      title: 'Create Test',
      icon: Icons.assignment_add,
      route: '/add-test',
      color: Colors.orange,
    ),
    DashboardAction(
      title: 'Add Course',
      icon: Icons.school_outlined,
      route: '/add-course',
      color: Colors.green,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Sized<PERSON>ox(height: 16),
        LayoutBuilder(
          builder: (context, constraints) {
            final isWide = constraints.maxWidth > 800;
            final crossAxisCount = isWide ? 4 : 2;

            return GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.8,
              children: _actions.map((action) => DashboardActionCard(
                title: action.title,
                icon: action.icon,
                color: action.color,
                onTap: () => context.go(action.route),
              )).toList(),
            );
          },
        ),
      ],
    );
  }
}
