import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class DashboardStatCard extends ConsumerWidget {
  const DashboardStatCard({
    super.key,
    required this.title,
    required this.icon,
    required this.color,
    required this.asyncValue,
    required this.getValue,
    required this.onTap,
  });

  final String title;
  final IconData icon;
  final Color color;
  final AsyncValue<dynamic> asyncValue;
  final String Function(dynamic) getValue;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: color.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: asyncValue.when(
            data: (data) => _buildDataContent(context, data),
            loading: () => _buildLoadingContent(),
            error: (error, stack) => _buildErrorContent(context),
          ),
        ),
      ),
    );
  }

  Widget _buildDataContent(BuildContext context, dynamic data) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Icon(
              icon,
              color: color,
              size: 28,
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: color.withValues(alpha: 0.5),
              size: 16,
            ),
          ],
        ),
        const Spacer(),
        Text(
          getValue(data),
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          title,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CircularProgressIndicator(
          color: color,
          strokeWidth: 2,
        ),
        const SizedBox(height: 8),
        Text(
          'Loading...',
          style: TextStyle(
            color: color.withValues(alpha: 0.7),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildErrorContent(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.error_outline,
          color: Colors.red.withValues(alpha: 0.5),
          size: 32,
        ),
        const SizedBox(height: 8),
        Text(
          'Error',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.red.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }
}
