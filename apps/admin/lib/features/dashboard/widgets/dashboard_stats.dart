import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../courses/providers/course_provider.dart';
import '../../tests/providers/simple_test_provider.dart';
import 'dashboard_stat_card.dart';

class DashboardStats extends ConsumerWidget {
  const DashboardStats({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'System Overview',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Sized<PERSON>ox(height: 16),
        LayoutBuilder(
          builder: (context, constraints) {
            final isWide = constraints.maxWidth > 1200;
            final crossAxisCount = isWide ? 4 : 2;

            return GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.5,
              children: [
                DashboardStatCard(
                  title: 'Courses',
                  icon: Icons.school,
                  color: Colors.green,
                  asyncValue: ref.watch(courseProvider).when<AsyncValue<int>>(
                    data: (courses) => AsyncData(courses.courses.length),
                    loading: () => const AsyncLoading(),
                    error: (error, stack) => AsyncError(error, stack),
                  ),
                  getValue: (count) => count.toString(),
                  onTap: () => context.go('/courses'),
                ),
                DashboardStatCard(
                  title: 'Tests',
                  icon: Icons.assignment,
                  color: Colors.orange,
                  asyncValue: ref.watch(testNotifierProvider).when<AsyncValue<int>>(
                    data: (tests) => AsyncData(tests.length),
                    loading: () => const AsyncLoading(),
                    error: (error, stack) => AsyncError(error, stack),
                  ),
                  getValue: (count) => count.toString(),
                  onTap: () => context.go('/tests'),
                ),
              ],
            );
          },
        ),
      ],
    );
  }
}
