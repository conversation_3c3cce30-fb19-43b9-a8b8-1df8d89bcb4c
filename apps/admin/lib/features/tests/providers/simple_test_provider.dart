// apps/admin/lib/features/tests/providers/simple_test_provider.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/test_entity.dart';
import 'package:entities/test_enums.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../services/published_test_service.dart';

// Simple provider without code generation for now
final testNotifierProvider = StateNotifierProvider<TestNotifier, AsyncValue<List<TestEntity>>>((ref) {
  return TestNotifier();
});

class TestNotifier extends StateNotifier<AsyncValue<List<TestEntity>>> {
  TestNotifier() : super(const AsyncValue.loading()) {
    _loadTests();
  }

  Future<void> _loadTests() async {
    try {
      final tests = await _getTests();
      state = AsyncValue.data(tests);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<List<TestEntity>> _getTests() async {
    final snapshot = await FirebaseFirestore.instance
        .collection('tests')
        .orderBy('createdAt', descending: true)
        .get();

    return snapshot.docs
        .map((doc) => TestEntity.fromJson({...doc.data(), 'id': doc.id}))
        .toList();
  }

  Future<void> createTest(TestEntity test) async {
    state = const AsyncLoading();

    state = await AsyncValue.guard(() async {
      // Simple validation
      if (test.name.isEmpty || test.courseId.isEmpty || test.yearId.isEmpty ||
          test.subjectId.isEmpty || test.duration <= 0) {
        throw Exception('Please fill in all required fields');
      }

      // Check if there are at least 6 questions available for this test configuration
      final questionsQuery = await FirebaseFirestore.instance
          .collection('questions')
          .where('courseId', isEqualTo: test.courseId)
          .where('yearId', isEqualTo: test.yearId)
          .where('subjectId', isEqualTo: test.subjectId)
          .where('type', isEqualTo: test.type.name)
          .get();

      if (questionsQuery.docs.length < 6) {
        throw Exception('At least 6 questions are required to create a test. Found only ${questionsQuery.docs.length} ${test.type.name.toUpperCase()} questions for ${test.courseId} ${test.yearId} ${test.subjectId}.');
      }

      final docRef = FirebaseFirestore.instance.collection('tests').doc();
      await docRef.set(test.copyWith(
        id: docRef.id,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ).toJson());

      debugPrint('Test created successfully: ${test.name}');
      return _getTests();
    });
  }

  Future<void> updateTest(TestEntity test) async {
    state = const AsyncLoading();

    state = await AsyncValue.guard(() async {
      // Simple validation
      if (test.name.isEmpty || test.courseId.isEmpty || test.yearId.isEmpty ||
          test.subjectId.isEmpty || test.duration <= 0) {
        throw Exception('Please fill in all required fields');
      }

      await FirebaseFirestore.instance
          .collection('tests')
          .doc(test.id)
          .update(test.copyWith(updatedAt: DateTime.now()).toJson());

      debugPrint('Test updated successfully: ${test.name}');
      return _getTests();
    });
  }

  Future<void> deleteTest(String testId) async {
    state = const AsyncLoading();
    
    state = await AsyncValue.guard(() async {
      await FirebaseFirestore.instance
          .collection('tests')
          .doc(testId)
          .delete();
      
      debugPrint('Test deleted successfully: $testId');
      return _getTests();
    });
  }

  Future<void> publishTest(String testId) async {
    state = const AsyncLoading();
    
    state = await AsyncValue.guard(() async {
      final testDoc = await FirebaseFirestore.instance
          .collection('tests')
          .doc(testId)
          .get();
      
      if (!testDoc.exists) {
        throw Exception('Test not found');
      }
      
      final test = TestEntity.fromJson({...testDoc.data()!, 'id': testId});

      if (test.questionIds.length < 5) {
        throw Exception('Test must have at least 5 questions to be published. Current: ${test.questionIds.length}');
      }
      
      await FirebaseFirestore.instance
          .collection('tests')
          .doc(testId)
          .update({
        'status': TestStatus.published.name,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      try {
        final publishedTest = await PublishedTestService.assemblePublishedTest(testId: testId);
        await PublishedTestService.savePublishedTest(publishedTest);
        debugPrint('Test published successfully: ${test.name}');
      } catch (e) {
        debugPrint('Error publishing test: $e');
        await FirebaseFirestore.instance
            .collection('tests')
            .doc(testId)
            .update({'status': TestStatus.draft.name});
        throw Exception('Failed to publish test. Please try again.');
      }
      
      return _getTests();
    });
  }
}

// Simple provider for single test
final singleTestProvider = FutureProvider.family<TestEntity?, String>((ref, testId) async {
  final doc = await FirebaseFirestore.instance
      .collection('tests')
      .doc(testId)
      .get();

  if (!doc.exists) return null;

  return TestEntity.fromJson({...doc.data()!, 'id': doc.id});
});
