// apps/admin/lib/features/tests/screens/tests_screen.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../shared/widgets/app_button.dart';
import '../../../shared/widgets/status_messages.dart';
import '../providers/simple_test_provider.dart';
import '../widgets/simple_test_card.dart';

class TestsScreen extends ConsumerWidget {
  const TestsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final testsAsync = ref.watch(testNotifierProvider);

    return Scaffold(
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Tests ${testsAsync.when(
                      data: (tests) => '(${tests.length})',
                      loading: () => '(...)',
                      error: (_, __) => '(?)',
                    )}',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                AppButton(
                  label: 'Import Excel',
                  onPressed: () => context.push("/import-test"),
                  icon: Icons.upload_file,
                  variant: AppButtonVariant.outlined,
                ),
                const SizedBox(width: 12),
                AppButton(
                  label: 'Add Test',
                  onPressed: () => context.push("/add-test"),
                  icon: Icons.add,
                  variant: AppButtonVariant.filled,
                ),
              ],
            ),
          ),

          Expanded(
            child: testsAsync.when(
              data: (tests) {
                if (tests.isEmpty) {
                  return const MessageDisplay(
                    title: 'No Tests Found',
                    message: 'No tests created yet. Create your first test to get started.',
                    icon: Icons.assignment_outlined,
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(24),
                  itemCount: tests.length,
                  itemBuilder: (context, index) {
                    return SimpleTestCard(test: tests[index]);
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, _) => ErrorMessage(
                title: 'Failed to Load Tests',
                message: 'Unable to load tests. Please try again.',
                onRetry: () => ref.invalidate(testNotifierProvider),
                icon: Icons.assignment_late,
              ),
            ),
          ),
        ],
      ),
    );
  }
}


