// apps/admin/lib/features/tests/screens/view_test_screen.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../shared/widgets/app_button.dart';
import '../../../shared/utils/error_handler.dart';
import '../providers/simple_test_provider.dart';
import '../widgets/test_details_view.dart';

class ViewTestScreen extends ConsumerWidget {
  final String testId;

  const ViewTestScreen({required this.testId, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {

    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Details'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/tests'),
        ),
        actions: [
          AppButton(
            label: 'Edit',
            onPressed: () => context.push('/edit-test/$testId'),
            variant: AppButtonVariant.outlined,
            icon: Icons.edit,
          ),
          const SizedBox(width: 8),
          OutlinedButton.icon(
            onPressed: () => _showDeleteDialog(context, ref),
            icon: const Icon(Icons.delete, color: Colors.red),
            label: const Text('Delete', style: TextStyle(color: Colors.red)),
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: Colors.red),
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: TestDetailsView(testId: testId),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Test'),
        content: const Text(
          'Are you sure you want to delete this test? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(dialogContext).pop();
              try {
                await ref.read(testNotifierProvider.notifier).deleteTest(testId);
                if (context.mounted) {
                  ErrorHandler.showSuccessSnackBar(ref, 'Test deleted successfully');
                  context.go('/tests');
                }
              } catch (error) {
                if (context.mounted) {
                  ErrorHandler.showErrorSnackBar(ref, error.toString(), context: 'Delete Test');
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
