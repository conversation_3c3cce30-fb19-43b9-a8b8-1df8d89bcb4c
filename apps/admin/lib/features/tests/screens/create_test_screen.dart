// apps/admin/lib/features/tests/screens/create_test_screen.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:entities/question_entity.dart';
import 'package:entities/test_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:providers/courses_provider.dart';

import '../../../shared/widgets/app_button.dart';
import '../../../shared/providers/filter_provider.dart';
import '../providers/simple_test_provider.dart';
import '../../questions/providers/question_provider.dart';

class CreateTestScreen extends ConsumerStatefulWidget {
  final String? testId;

  const CreateTestScreen({super.key, this.testId});

  @override
  ConsumerState<CreateTestScreen> createState() => _CreateTestScreenState();
}

class _CreateTestScreenState extends ConsumerState<CreateTestScreen> {
  // Local form state - Flutter 2025 best practice
  String name = '';
  String courseId = '';
  String yearId = '';
  String subjectId = '';
  QuestionType type = QuestionType.mcq; // Will be initialized from filter
  int duration = 60;

  // Loading state for save operation
  bool isSaving = false;

  // Selected questions for the test
  Set<String> selectedQuestions = {};

  @override
  void initState() {
    super.initState();
    // Initialize form state from current filter on next frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (isEditMode) {
        _initializeFromExistingTest();
      } else {
        _initializeFromFilter();
      }
    });
  }

  void _initializeFromFilter() {
    final filter = ref.read(filterProvider);

    setState(() {
      // Initialize form from current filter state - user preference
      courseId = filter.courseId ?? '';
      yearId = filter.yearId ?? '';
      subjectId = filter.subjectId ?? '';
      type = filter.type ?? QuestionType.mcq; // Use selected filter type or default to MCQ
    });
  }

  void _initializeFromExistingTest() async {
    if (widget.testId == null) return;

    try {
      final testAsync = await ref.read(singleTestProvider(widget.testId!).future);
      if (testAsync != null) {
        setState(() {
          name = testAsync.name;
          courseId = testAsync.courseId;
          yearId = testAsync.yearId;
          subjectId = testAsync.subjectId;
          type = testAsync.type;
          duration = testAsync.duration;
          selectedQuestions = testAsync.questionIds.toSet(); // Load existing questions
        });
      }
    } catch (e) {
      // Handle error loading test data
      debugPrint('Error loading test data: $e');
    }
  }

  bool get isEditMode => widget.testId != null;

  // Simple validation - must have at least 6 questions for test creation
  String? get validationError {
    if (name.isEmpty) return 'Test name is required';
    if (courseId.isEmpty) return 'Please select a course';
    if (yearId.isEmpty) return 'Please select a year';
    if (subjectId.isEmpty) return 'Please select a subject';
    if (duration <= 0) return 'Duration must be greater than 0';
    return null;
  }

  // Additional validation for question count (checked before save)
  Future<String?> get questionCountValidationError async {
    try {
      // Check if at least 6 questions are selected
      if (selectedQuestions.length < 6) {
        return 'At least 6 questions must be selected to create a test. Currently ${selectedQuestions.length} questions are selected.';
      }

      return null;
    } catch (e) {
      return 'Error checking selected questions. Please try again.';
    }
  }

  bool get isValid => validationError == null;

  // Navigate to Add Question screen with current form values
  void _navigateToAddQuestion() async {
    final typeStr = type == QuestionType.mcq ? 'mcq' : 'flipCard';

    // Navigate to add question screen with current form context
    final result = await context.push(
      '/add-question?courseId=$courseId&yearId=$yearId&subjectId=$subjectId&type=$typeStr'
    );

    // If a question was created, show success message
    if (result != null && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Question added successfully!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final coursesAsync = ref.watch(coursesProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditMode ? 'Edit Test' : 'Create Test'),
        automaticallyImplyLeading: false,
        actions: [
          TextButton(
            onPressed: () => context.pop(),
            child: const Text('Cancel'),
          ),
          const SizedBox(width: 8),
          AppButton(
            label: isSaving ? 'Saving...' : 'Save Test',
            onPressed: isValid && !isSaving
                ? () => _saveTest(context)
                : null,
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Test Information',
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.info_outline, color: Colors.blue.shade700, size: 20),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Select at least 6 questions from the Questions section below to create a test',
                              style: TextStyle(
                                color: Colors.blue.shade700,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Test Name - Simple TextField with local state
                    TextField(
                      onChanged: (value) => setState(() => name = value),
                      decoration: const InputDecoration(
                        labelText: 'Test Name',
                        border: OutlineInputBorder(),
                        hintText: 'Enter test name...',
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Course Selection
                    coursesAsync.when(
                      data: (courses) => _buildCourseDropdowns(context, courses),
                      loading: () => const CircularProgressIndicator(),
                      error: (_, __) => const Text('Error loading courses'),
                    ),
                    const SizedBox(height: 16),

                    // Duration and Type
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            onChanged: (value) {
                              final newDuration = int.tryParse(value) ?? 0;
                              setState(() => duration = newDuration);
                            },
                            controller: TextEditingController(text: duration.toString()),
                            decoration: const InputDecoration(
                              labelText: 'Duration (minutes)',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.number,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: DropdownButtonFormField<QuestionType>(
                            value: type,
                            decoration: const InputDecoration(
                              labelText: 'Question Type',
                              border: OutlineInputBorder(),
                            ),
                            items: QuestionType.values.map<DropdownMenuItem<QuestionType>>((questionType) {
                              return DropdownMenuItem<QuestionType>(
                                value: questionType,
                                child: Text(questionType == QuestionType.mcq ? 'MCQ' : 'Flip Card'),
                              );
                            }).toList(),
                            onChanged: (value) {
                              if (value != null) {
                                setState(() => type = value);
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Questions Section (separate from test metadata)
            const SizedBox(height: 24),
            if (courseId.isNotEmpty && yearId.isNotEmpty && subjectId.isNotEmpty) ...[
              _buildQuestionsSection(),
            ],
          ],
        ),
      ),
    );
  }



  Widget _buildCourseDropdowns(BuildContext context, courses) {
    final courseNames = courses.courses.keys.toList();

    return Column(
      children: [
        // Course dropdown
        DropdownButtonFormField<String>(
          value: courseId.isEmpty ? null : courseId,
          decoration: const InputDecoration(
            labelText: 'Course',
            border: OutlineInputBorder(),
          ),
          items: courseNames.map<DropdownMenuItem<String>>((course) {
            return DropdownMenuItem<String>(value: course, child: Text(course));
          }).toList(),
          onChanged: (value) {
            setState(() {
              courseId = value ?? '';
              // Reset dependent fields
              yearId = '';
              subjectId = '';
            });
          },
        ),
        const SizedBox(height: 16),

        // Year dropdown
        if (courseId.isNotEmpty)
          DropdownButtonFormField<String>(
            value: yearId.isEmpty ? null : yearId,
            decoration: const InputDecoration(
              labelText: 'Year',
              border: OutlineInputBorder(),
            ),
            items: courses.courses[courseId]?.years.keys.map<DropdownMenuItem<String>>((year) {
              return DropdownMenuItem<String>(value: year, child: Text(year));
            }).toList() ?? [],
            onChanged: (value) {
              setState(() {
                yearId = value ?? '';
                // Reset dependent field
                subjectId = '';
              });
            },
          ),
        if (courseId.isNotEmpty) const SizedBox(height: 16),

        // Subject dropdown
        if (yearId.isNotEmpty)
          DropdownButtonFormField<String>(
            value: subjectId.isEmpty ? null : subjectId,
            decoration: const InputDecoration(
              labelText: 'Subject',
              border: OutlineInputBorder(),
            ),
            items: courses.courses[courseId]?.years[yearId]?.subjects.keys.map<DropdownMenuItem<String>>((subject) {
              return DropdownMenuItem<String>(value: subject, child: Text(subject));
            }).toList() ?? [],
            onChanged: (value) {
              setState(() => subjectId = value ?? '');
            },
          ),
      ],
    );
  }

  // Questions Section - shows existing questions and allows adding new ones
  Widget _buildQuestionsSection() {
    final typeStr = type == QuestionType.mcq ? 'mcq' : 'flipCard';

    // Get questions query for current selection
    final questionsQuery = ref.watch(filteredQuestionsQueryProvider(
      courseId: courseId,
      yearId: yearId,
      subjectId: subjectId,
      type: typeStr,
    ));

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade100,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              Icon(Icons.quiz_outlined, color: Colors.blue.shade700, size: 24),
              const SizedBox(width: 12),
              Text(
                'Questions',
                style: TextStyle(
                  color: Colors.grey.shade800,
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (selectedQuestions.isNotEmpty) ...[
                const SizedBox(width: 12),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${selectedQuestions.length} selected',
                    style: TextStyle(
                      color: Colors.blue.shade700,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
              const Spacer(),
              AppButton(
                label: 'Add New Question',
                onPressed: () => _navigateToAddQuestion(),
                icon: Icons.add,
                variant: AppButtonVariant.outlined,
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Questions List
          _buildQuestionsList(questionsQuery),
        ],
      ),
    );
  }

  // Build questions list widget
  Widget _buildQuestionsList(Query<QuestionEntity> questionsQuery) {
    return StreamBuilder<QuerySnapshot<QuestionEntity>>(
      stream: questionsQuery.snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (snapshot.hasError) {
          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.shade200),
            ),
            child: Text(
              'Error loading questions: ${snapshot.error}',
              style: TextStyle(color: Colors.red.shade700),
            ),
          );
        }

        final questions = snapshot.data?.docs ?? [];

        if (questions.isEmpty) {
          return Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              children: [
                Icon(Icons.quiz_outlined, size: 48, color: Colors.grey.shade400),
                const SizedBox(height: 16),
                Text(
                  'No questions found',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Add questions for $courseId $yearId $subjectId (${type == QuestionType.mcq ? 'MCQ' : 'FlipCard'})',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Questions count and Select All
            Row(
              children: [
                Text(
                  '${questions.length} question${questions.length == 1 ? '' : 's'} available',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                if (questions.isNotEmpty) ...[
                  TextButton.icon(
                    onPressed: () {
                      setState(() {
                        final allQuestionIds = questions
                            .map((doc) => doc.data().id ?? '')
                            .where((id) => id.isNotEmpty)
                            .toSet();

                        if (selectedQuestions.length == allQuestionIds.length) {
                          // Deselect all
                          selectedQuestions.clear();
                        } else {
                          // Select all
                          selectedQuestions.addAll(allQuestionIds);
                        }
                      });
                    },
                    icon: Icon(
                      selectedQuestions.length == questions.length
                          ? Icons.deselect
                          : Icons.select_all,
                      size: 16,
                    ),
                    label: Text(
                      selectedQuestions.length == questions.length
                          ? 'Deselect All'
                          : 'Select All',
                      style: const TextStyle(fontSize: 12),
                    ),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 12),

            // Questions list
            Container(
              constraints: const BoxConstraints(maxHeight: 300),
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: questions.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final question = questions[index].data();
                  return _buildQuestionItem(question, index);
                },
              ),
            ),
          ],
        );
      },
    );
  }

  // Modern Flutter 2025 save method using local state
  Future<void> _saveTest(BuildContext context) async {
    if (!isValid) return;

    setState(() => isSaving = true);

    try {
      // Check question count requirement for new tests
      if (!isEditMode) {
        final questionError = await questionCountValidationError;
        if (questionError != null) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(questionError),
                backgroundColor: Colors.orange,
                duration: const Duration(seconds: 5),
              ),
            );
          }
          return;
        }
      }

      // Create test entity from local state
      final test = TestEntity(
        name: name,
        courseId: courseId,
        yearId: yearId,
        subjectId: subjectId,
        type: type,
        duration: duration,
        questionIds: selectedQuestions.toList(), // Include selected questions
      );

      // Use simple test provider directly
      final testNotifier = ref.read(testNotifierProvider.notifier);

      if (isEditMode) {
        await testNotifier.updateTest(test.copyWith(
          id: widget.testId,
          questionIds: selectedQuestions.toList(), // Update selected questions in edit mode too
        ));
      } else {
        await testNotifier.createTest(test);
      }

      if (context.mounted) {
        context.pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isEditMode ? 'Test updated successfully!' : 'Test created successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_getErrorMessage(e)),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => isSaving = false);
      }
    }
  }

  // Simple error message helper
  String _getErrorMessage(Object error) {
    final errorStr = error.toString();

    if (errorStr.contains('required fields')) return 'Please fill in all required fields';
    if (errorStr.contains('6 questions')) return errorStr.replaceFirst('Exception: ', '');
    if (errorStr.contains('At least 6 questions are required')) return errorStr.replaceFirst('Exception: ', '');
    if (errorStr.contains('not found')) return 'Test not found. It may have been deleted.';
    if (errorStr.contains('network') || errorStr.contains('connection')) {
      return 'Network error. Please check your connection and try again.';
    }

    return 'Something went wrong. Please try again.';
  }

  // Build individual question item
  Widget _buildQuestionItem(QuestionEntity question, int index) {
    final questionId = question.id ?? '';
    final isSelected = selectedQuestions.contains(questionId);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.shade50 : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: isSelected ? Border.all(color: Colors.blue.shade200) : null,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Selection checkbox
          Checkbox(
            value: isSelected,
            onChanged: questionId.isEmpty ? null : (bool? value) {
              setState(() {
                if (value == true) {
                  selectedQuestions.add(questionId);
                } else {
                  selectedQuestions.remove(questionId);
                }
              });
            },
            activeColor: Colors.blue.shade600,
          ),

          // Question number
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                '${index + 1}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue.shade700,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),

          // Question content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  question.question,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: question.type == QuestionType.mcq
                            ? Colors.green.shade100
                            : Colors.purple.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        question.type == QuestionType.mcq ? 'MCQ' : 'FlipCard',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: question.type == QuestionType.mcq
                              ? Colors.green.shade700
                              : Colors.purple.shade700,
                        ),
                      ),
                    ),
                    if (question.topic != null) ...[
                      const SizedBox(width: 8),
                      Text(
                        question.topic!,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          // Action buttons
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: () => _editQuestion(question),
                icon: Icon(Icons.edit_outlined, size: 18, color: Colors.blue.shade600),
                tooltip: 'Edit Question',
              ),
              IconButton(
                onPressed: () => _deleteQuestion(question),
                icon: Icon(Icons.delete_outline, size: 18, color: Colors.red.shade600),
                tooltip: 'Delete Question',
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Edit question
  void _editQuestion(QuestionEntity question) async {
    final typeStr = question.type == QuestionType.mcq ? 'mcq' : 'flipCard';

    final result = await context.push(
      '/edit-question/${question.id}?courseId=${question.courseId}&yearId=${question.yearId}&subjectId=${question.subjectId}&type=$typeStr'
    );

    if (result != null && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Question updated successfully!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  // Delete question
  void _deleteQuestion(QuestionEntity question) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Question'),
        content: const Text('Are you sure you want to delete this question? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(deleteQuestionProvider(question.id!).future);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Question deleted successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting question: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
