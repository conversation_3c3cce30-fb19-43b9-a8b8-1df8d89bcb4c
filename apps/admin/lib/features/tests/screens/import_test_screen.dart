
import 'package:excel/excel.dart' hide Border, BorderStyle;
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../shared/widgets/app_button.dart';
// import '../../../shared/services/section_service.dart'; // Disabled due to compilation errors
import 'package:entities/entities.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:providers/courses_provider.dart';
import '../../../shared/providers/filter_helpers_provider.dart';

/// Results of import operation
class ImportResults {
  int successful = 0;
  int failed = 0;
  List<String> errors = [];
}

/// Simple import screen for Excel files - shows actual imported data
class ImportTestScreen extends ConsumerStatefulWidget {
  const ImportTestScreen({super.key});

  @override
  ConsumerState<ImportTestScreen> createState() => _ImportTestScreenState();
}

class _ImportTestScreenState extends ConsumerState<ImportTestScreen> {
  List<Map<String, dynamic>> _previewData = [];
  List<String> _headers = [];
  bool _isLoading = false;
  String? _fileName;

  // Expected column names (exact match required)
  static const List<String> _expectedColumns = [
    'QUESTION',
    'ANSWER 1',
    'ANSWER 2',
    'ANSWER 3',
    'ANSWER 4',
    'ANSWER 5',
    'CORRECT ANSWER',
    'DETAILED EXPLANATION',
    'TOPIC',
    'DIFFICULTY RATING',
    'SUBJECT',
    'COURSE',
    'YEAR',
    'Front',
    'Back',
    'SECTION',
  ];

  // Helper method to check if a column is recognized
  bool _isRecognizedColumn(String columnName) {
    final normalizedColumn = columnName.trim().toUpperCase();
    final normalizedExpected = _expectedColumns.map((col) => col.trim().toUpperCase()).toList();

    return normalizedExpected.contains(normalizedColumn);
  }

  // Auto-detect question type from headers
  String _detectQuestionType(List<String> headers) {
    final normalizedHeaders = headers.map((h) => h.trim().toUpperCase()).toList();

    bool hasMCQColumns =
        normalizedHeaders.contains("QUESTION") && normalizedHeaders.contains("ANSWER 1");
    bool hasFlipCardColumns =
        normalizedHeaders.contains("FRONT") && normalizedHeaders.contains("BACK");

    if (hasMCQColumns && !hasFlipCardColumns) return "MCQ";
    if (hasFlipCardColumns && !hasMCQColumns) return "FlipCard";

    throw Exception("Cannot determine question type from columns");
  }

  // Get count of recognized vs ignored columns
  Map<String, int> _getColumnStats() {
    int recognized = 0;
    int ignored = 0;

    for (String header in _headers) {
      if (_isRecognizedColumn(header)) {
        recognized++;
      } else {
        ignored++;
      }
    }

    return {'recognized': recognized, 'ignored': ignored};
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Import Excel Data'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          if (_previewData.isNotEmpty) ...[
            TextButton(
              onPressed: () => context.pop(),
              child: const Text('Cancel'),
            ),
            const SizedBox(width: 8),
            AppButton(
              label: 'Import Data',
              onPressed: _importData,
              isLoading: _isLoading,
            ),
            const SizedBox(width: 16),
          ],
        ],
      ),
      body: _previewData.isEmpty ? _buildUploadArea() : _buildPreviewTable(),
    );
  }

  Widget _buildUploadArea() {
    return Center(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 600),
        padding: const EdgeInsets.all(32),
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Upload Icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(60),
                ),
                child: const Icon(
                  Icons.cloud_upload_outlined,
                  size: 60,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(height: 24),

              // Title
              const Text(
                'Import Excel Data',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),

              // Subtitle
              Text(
                'Upload an Excel (.xlsx) file to preview and import your data',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // Upload Button
              AppButton(
                label: 'Choose Excel File',
                onPressed: _pickFile,
                isLoading: _isLoading,
                icon: Icons.file_upload,
              ),
              const SizedBox(height: 32),

              // Simple Info
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline,
                            color: Colors.blue[700], size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Import Information',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.blue[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '• Upload any Excel file (.xlsx)\n'
                      '• We will show you exactly what\'s in your file\n'
                      '• No specific format required\n'
                      '• Preview your data before importing',
                      style: TextStyle(fontSize: 13),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPreviewTable() {
    return Column(
      children: [
        // Header with file info and actions
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
          ),
          child: Row(
            children: [
              Icon(Icons.description, color: Colors.blue),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _fileName ?? 'Imported File',
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                    Text(
                      '${_previewData.length} rows found',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    // Column statistics
                    Builder(
                      builder: (context) {
                        final stats = _getColumnStats();
                        return Text(
                          '${stats['recognized']} recognized, ${stats['ignored']} ignored columns',
                          style:
                              TextStyle(color: Colors.grey[600], fontSize: 12),
                        );
                      },
                    ),
                    // Question type detection
                    Builder(
                      builder: (context) {
                        try {
                          final questionType = _detectQuestionType(_headers);
                          return Text(
                            'Detected: $questionType questions',
                            style: TextStyle(
                              color: Colors.blue[600],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          );
                        } catch (e) {
                          return Text(
                            'Error: ${e.toString().replaceAll('Exception: ', '')}',
                            style: TextStyle(
                              color: Colors.red[600],
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          );
                        }
                      },
                    ),
                  ],
                ),
              ),
              TextButton.icon(
                onPressed: _clearImport,
                icon: const Icon(Icons.clear),
                label: const Text('Clear'),
              ),
            ],
          ),
        ),

        // Legend
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 16),
              const SizedBox(width: 4),
              Text('Recognized',
                  style: TextStyle(fontSize: 12, color: Colors.grey[700])),
              const SizedBox(width: 16),
              Icon(Icons.cancel, color: Colors.orange, size: 16),
              const SizedBox(width: 4),
              Text('Ignored',
                  style: TextStyle(fontSize: 12, color: Colors.grey[700])),
            ],
          ),
        ),

        // Preview Table - Simple display of actual data with proper scrolling
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SizedBox(
                width:
                    _headers.length * 150.0, // Dynamic width based on columns
                child: SingleChildScrollView(
                  scrollDirection: Axis.vertical,
                  child: DataTable(
                    columnSpacing: 20,
                    headingRowHeight: 50,
                    dataRowMinHeight: 45,
                    dataRowMaxHeight: 60,
                    columns: _headers
                        .map((header) => DataColumn(
                              label: SizedBox(
                                width: 130,
                                child: Row(
                                  children: [
                                    // Status indicator
                                    Icon(
                                      _isRecognizedColumn(header)
                                          ? Icons.check_circle
                                          : Icons.cancel,
                                      color: _isRecognizedColumn(header)
                                          ? Colors.green
                                          : Colors.orange,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 4),
                                    // Column name
                                    Expanded(
                                      child: Text(
                                        header,
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 14,
                                          color: _isRecognizedColumn(header)
                                              ? Colors.black
                                              : Colors.grey[600],
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ))
                        .toList(),
                    rows: _previewData.map((row) {
                      return DataRow(
                        cells: _headers.map((header) {
                          final value = row[header] ?? '';
                          return DataCell(
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 4, horizontal: 2),
                              child: SizedBox(
                                width: 130,
                                child: Text(
                                  value.toString(),
                                  style: const TextStyle(fontSize: 13),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _pickFile() async {
    setState(() => _isLoading = true);

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.bytes != null) {
          if (file.extension?.toLowerCase() == 'xlsx') {
            await _parseExcel(file.bytes!, file.name);
          } else {
            _showError('Please select an Excel (.xlsx) file.');
          }
        } else {
          _showError('Could not read file content');
        }
      }
    } catch (e) {
      _showError('Error picking file: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _parseExcel(Uint8List bytes, String fileName) async {
    try {
      // Parse Excel file with error handling for custom number formats
      Excel excel;
      try {
        excel = Excel.decodeBytes(bytes);
      } catch (e) {
        // If parsing fails due to custom formats, show a more helpful error
        if (e.toString().contains('numFmtId') ||
            e.toString().contains('custom')) {
          _showError(
              'Excel file contains unsupported number formats. Please save as a simple .xlsx file without custom formatting.');
        } else {
          _showError('Failed to parse Excel file: ${e.toString()}');
        }
        return;
      }

      if (excel.tables.isEmpty) {
        _showError('Excel file has no sheets');
        return;
      }

      // Get the first sheet
      final sheet = excel.tables.values.first;
      if (sheet.rows.isEmpty) {
        _showError('Excel sheet is empty');
        return;
      }

      // Get headers from first row - show exactly what's there
      final headerRow = sheet.rows.first;
      final headers = <String>[];
      for (int j = 0; j < headerRow.length; j++) {
        final cell = headerRow[j];
        // Get the raw value exactly as it appears
        final headerValue = cell?.value?.toString() ?? 'Column ${j + 1}';
        headers.add(headerValue);
      }

      // Get data rows (skip header row) - show exactly what's there
      final rows = <Map<String, dynamic>>[];
      for (int i = 1; i < sheet.rows.length; i++) {
        final row = sheet.rows[i];
        final Map<String, dynamic> rowMap = {};

        for (int j = 0; j < headers.length; j++) {
          // Get the raw cell value exactly as it appears
          final cell = j < row.length ? row[j] : null;
          final cellValue = cell?.value?.toString() ?? '';
          rowMap[headers[j]] = cellValue;
        }

        // Add ALL rows, even empty ones - show exactly what's in the file
        rows.add(rowMap);
      }

      setState(() {
        _headers = headers;
        _previewData = rows;
        _fileName = fileName;
      });
    } catch (e) {
      _showError('Error parsing Excel file: $e');
    }
  }

  void _importData() async {
    if (_previewData.isEmpty) return;

    setState(() => _isLoading = true);

    try {
      // Detect question type
      final questionType = _detectQuestionType(_headers);

      // Process all questions
      final results = await _processImportData(questionType);

      // Show results
      _showImportResults(results);
    } catch (e) {
      _showError('Import failed: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }



  void _clearImport() {
    setState(() {
      _previewData.clear();
      _headers.clear();
      _fileName = null;
    });
  }

  /// Process import data and create questions using all-or-nothing strategy
  Future<ImportResults> _processImportData(String questionType) async {
    final results = ImportResults();
    debugPrint('DEBUG: ==================== STARTING IMPORT ====================');
    debugPrint('DEBUG: Question type: $questionType');
    debugPrint('DEBUG: Total rows to process: ${_previewData.length}');
    debugPrint('DEBUG: Using ALL-OR-NOTHING strategy');

    // First pass: Validate all rows without creating anything
    final validatedQuestions = <QuestionEntity>[];
    final courseStructures = <Map<String, dynamic>>[];

    debugPrint('DEBUG: ==================== VALIDATION PHASE ====================');
    for (int i = 0; i < _previewData.length; i++) {
      debugPrint('DEBUG: -------------------- Validating Row ${i + 1} --------------------');

      try {
        final row = _previewData[i];
        debugPrint('DEBUG: Row data keys: ${row.keys.toList()}');

        // Extract required fields
        final courseId = _getValueFromRow(row, 'COURSE')?.trim();
        final yearId = _getValueFromRow(row, 'YEAR')?.trim();
        final subjectName = _getValueFromRow(row, 'SUBJECT')?.trim();
        final sectionName = _getValueFromRow(row, 'SECTION')?.trim();
        final topicName = _getValueFromRow(row, 'TOPIC')?.trim();

        debugPrint('DEBUG: Extracted fields:');
        debugPrint('DEBUG:   courseId="$courseId"');
        debugPrint('DEBUG:   yearId="$yearId"');
        debugPrint('DEBUG:   subjectName="$subjectName"');
        debugPrint('DEBUG:   sectionName="$sectionName"');
        debugPrint('DEBUG:   topicName="$topicName"');

        // Validate required fields
        if (courseId == null || courseId.isEmpty ||
            yearId == null || yearId.isEmpty ||
            subjectName == null || subjectName.isEmpty) {
          debugPrint('DEBUG: Row ${i + 1}: Missing required fields - FAILING ENTIRE IMPORT');
          results.failed = _previewData.length;
          results.errors.add('Row ${i + 1}: Missing required fields (COURSE, YEAR, SUBJECT) - Import failed');
          return results;
        }

        // Generate subject ID
        final subjectId = _generateSubjectId(subjectName, yearId, courseId);
        debugPrint('DEBUG: Generated subjectId="$subjectId"');

        // Store course structure info for later creation
        courseStructures.add({
          'courseId': courseId,
          'yearId': yearId,
          'subjectId': subjectId,
          'subjectName': subjectName,
          'sectionName': sectionName,
          'topicName': topicName,
        });

        // Create question entity for validation
        debugPrint('DEBUG: Creating question entity for validation...');
        QuestionEntity? question;
        if (questionType == 'MCQ') {
          question = _createMCQQuestion(row, courseId, yearId, subjectName, sectionName, topicName);
        } else if (questionType == 'FlipCard') {
          question = _createFlipCardQuestion(row, courseId, yearId, subjectName, sectionName, topicName);
        }

        if (question == null) {
          debugPrint('DEBUG: Row ${i + 1}: Failed to create question entity - FAILING ENTIRE IMPORT');
          results.failed = _previewData.length;
          results.errors.add('Row ${i + 1}: Failed to create question entity - Import failed');
          return results;
        }

        // No question duplicate check needed - only course structure duplication is handled

        validatedQuestions.add(question);
        debugPrint('DEBUG: Row ${i + 1}: Validation SUCCESS');

      } catch (e, stackTrace) {
        debugPrint('DEBUG: ==================== VALIDATION ERROR ====================');
        debugPrint('DEBUG: Row ${i + 1} validation failed');
        debugPrint('DEBUG: Error type: ${e.runtimeType}');
        debugPrint('DEBUG: Error details: $e');
        debugPrint('DEBUG: Stack trace: $stackTrace');
        debugPrint('DEBUG: ========================================================');

        results.failed = _previewData.length;
        results.errors.add('Row ${i + 1}: ${e.toString()} - Import failed');
        return results;
      }
    }

    debugPrint('DEBUG: ==================== CREATION PHASE ====================');
    debugPrint('DEBUG: All rows validated successfully, proceeding with creation...');

    // Second pass: Create course structures and questions
    try {
      // Create unique course structures
      final uniqueStructures = <String, Map<String, dynamic>>{};
      for (final structure in courseStructures) {
        final key = '${structure['courseId']}_${structure['yearId']}_${structure['subjectName']}_${structure['sectionName']}_${structure['topicName']}';
        uniqueStructures[key] = structure;
      }

      debugPrint('DEBUG: Creating ${uniqueStructures.length} unique course structures...');
      for (final structure in uniqueStructures.values) {
        await _ensureBasicCourseStructure(
          courseId: structure['courseId'],
          yearId: structure['yearId'],
          subjectId: structure['subjectId'],
          subjectName: structure['subjectName'],
          sectionName: structure['sectionName'],
          topicName: structure['topicName'],
        );
      }

      debugPrint('DEBUG: Creating ${_previewData.length} questions with corrected course structure values...');
      for (int i = 0; i < _previewData.length; i++) {
        final row = _previewData[i];

        // Extract fields again to get corrected values
        final courseId = (row['COURSE'] as String).trim();
        final yearId = (row['YEAR'] as String).trim();
        final subjectName = (row['SUBJECT'] as String).trim();
        final sectionName = row['SECTION'] != null ? (row['SECTION'] as String).trim() : null;
        final topicName = row['CHAPTER/TOPIC'] != null ? (row['CHAPTER/TOPIC'] as String).trim() : null;
        final questionType = _getQuestionType();

        // Get corrected values from course structure
        final correctedValues = await _getCorrectedCourseValues(courseId, yearId, subjectName);

        // Create question with corrected values
        QuestionEntity? question;
        if (questionType == 'MCQ') {
          question = _createMCQQuestion(row, courseId, correctedValues['yearId']!, correctedValues['subjectName']!, sectionName, topicName);
        } else if (questionType == 'FlipCard') {
          question = _createFlipCardQuestion(row, courseId, correctedValues['yearId']!, correctedValues['subjectName']!, sectionName, topicName);
        }

        if (question != null) {
          await _saveQuestionToFirestore(question);
          debugPrint('DEBUG: Question ${i + 1}/${_previewData.length} created successfully');
        }
      }

      results.successful = validatedQuestions.length;
      debugPrint('DEBUG: All questions created successfully!');

    } catch (e, stackTrace) {
      debugPrint('DEBUG: ==================== CREATION ERROR ====================');
      debugPrint('DEBUG: Error during creation phase');
      debugPrint('DEBUG: Error type: ${e.runtimeType}');
      debugPrint('DEBUG: Error details: $e');
      debugPrint('DEBUG: Stack trace: $stackTrace');
      debugPrint('DEBUG: ========================================================');

      results.failed = _previewData.length;
      results.successful = 0;
      results.errors.add('Creation failed: ${e.toString()} - Import failed');
      return results;
    }

    debugPrint('DEBUG: ==================== IMPORT COMPLETED ====================');
    debugPrint('DEBUG: Successful: ${results.successful}');
    debugPrint('DEBUG: Failed: ${results.failed}');
    debugPrint('DEBUG: Errors: ${results.errors.length}');

    // If any questions were successfully imported, invalidate the courses provider
    // so that filter dropdowns will refresh and show the new courses/years/subjects
    if (results.successful > 0) {
      debugPrint('DEBUG: Invalidating courses cache to refresh filters...');
      try {
        // Invalidate the courses provider (admin uses direct Firestore, no caching)
        ref.invalidate(coursesProvider);
        // Invalidate filter helper providers so dropdowns refresh
        ref.invalidate(availableCoursesProvider);
        ref.invalidate(availableYearsProvider);
        ref.invalidate(availableSubjectsProvider);
        ref.invalidate(availableTopicsProvider);
        debugPrint('DEBUG: Courses provider and filter providers invalidated successfully');
      } catch (e) {
        debugPrint('DEBUG: Error invalidating courses provider: $e');
      }
    }

    debugPrint('DEBUG: =========================================================');

    return results;
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message)),
      );
    }
  }

  /// Get value from row by column name
  String? _getValueFromRow(Map<String, dynamic> row, String columnName) {
    // Try exact match first
    if (row.containsKey(columnName)) {
      return row[columnName]?.toString();
    }

    // Try case-insensitive match
    for (final key in row.keys) {
      if (key.trim().toUpperCase() == columnName.trim().toUpperCase()) {
        return row[key]?.toString();
      }
    }

    return null;
  }

  /// Generate subject ID from name, year, and course
  String _generateSubjectId(String subjectName, String yearId, String courseId) {
    final generated = '${courseId.replaceAll(' ', '_').toLowerCase()}_'
           '${yearId.replaceAll(' ', '_').toLowerCase()}_'
           '${subjectName.replaceAll(' ', '_').toLowerCase()}';

    debugPrint('DEBUG: Subject ID generation:');
    debugPrint('DEBUG:   courseId="$courseId" → "${courseId.replaceAll(' ', '_').toLowerCase()}"');
    debugPrint('DEBUG:   yearId="$yearId" → "${yearId.replaceAll(' ', '_').toLowerCase()}"');
    debugPrint('DEBUG:   subjectName="$subjectName" → "${subjectName.replaceAll(' ', '_').toLowerCase()}"');
    debugPrint('DEBUG:   Generated subjectId="$generated"');

    return generated;
  }

  /// Ensure course structure exists in the correct Firestore document
  /// Uses the proper course catalog structure: course_catalog/structure
  /// Structure: courses[courseName] -> years[yearName] -> subjects[subjectName] -> sections[sectionName] -> topics[topicName]
  Future<void> _ensureBasicCourseStructure({
    required String courseId,
    required String yearId,
    required String subjectId,
    required String subjectName,
    String? sectionName,
    String? topicName,
  }) async {
    debugPrint('DEBUG: Starting course structure creation...');
    debugPrint('DEBUG: courseId="$courseId", yearId="$yearId"');
    debugPrint('DEBUG: subjectId="$subjectId", subjectName="$subjectName"');
    debugPrint('DEBUG: sectionName="$sectionName", topicName="$topicName"');

    try {
      final firestore = FirebaseFirestore.instance;
      final docRef = firestore.collection('course_catalog').doc('structure');

      debugPrint('DEBUG: Getting course catalog from Firestore...');
      final snapshot = await docRef.get();
      debugPrint('DEBUG: Document exists: ${snapshot.exists}');

      // Load existing courses or create empty structure
      CoursesEntity coursesEntity;
      if (snapshot.exists && snapshot.data() != null) {
        coursesEntity = CoursesEntity.fromJson(snapshot.data()!);
        debugPrint('DEBUG: Loaded existing courses: ${coursesEntity.courses.keys.toList()}');
      } else {
        coursesEntity = CoursesEntity();
        debugPrint('DEBUG: Creating new course catalog structure');
      }

      // Get or create course
      CourseEntity courseEntity;
      if (coursesEntity.courses.containsKey(courseId)) {
        courseEntity = coursesEntity.courses[courseId]!;
        debugPrint('DEBUG: Course $courseId already exists');
      } else {
        courseEntity = CourseEntity();
        debugPrint('DEBUG: Creating new course: $courseId');
      }

      // Get or create year (case-insensitive check)
      YearEntity yearEntity;
      String? existingYearKey;

      // Find existing year with case-insensitive comparison
      for (final key in courseEntity.years.keys) {
        if (key.toLowerCase() == yearId.toLowerCase()) {
          existingYearKey = key;
          break;
        }
      }

      if (existingYearKey != null) {
        yearEntity = courseEntity.years[existingYearKey]!;
        debugPrint('DEBUG: Year $yearId already exists as "$existingYearKey"');
        // Use the existing year key for consistency
        yearId = existingYearKey;
      } else {
        yearEntity = YearEntity();
        debugPrint('DEBUG: Creating new year: $yearId');
      }

      // Get or create subject (case-insensitive check)
      SubjectEntity subjectEntity;
      String? existingSubjectKey;

      // Find existing subject with case-insensitive comparison
      for (final key in yearEntity.subjects.keys) {
        if (key.toLowerCase() == subjectName.toLowerCase()) {
          existingSubjectKey = key;
          break;
        }
      }

      if (existingSubjectKey != null) {
        subjectEntity = yearEntity.subjects[existingSubjectKey]!;
        debugPrint('DEBUG: Subject $subjectName already exists as "$existingSubjectKey"');
        // Use the existing subject key for consistency
        subjectName = existingSubjectKey;
      } else {
        subjectEntity = SubjectEntity();
        debugPrint('DEBUG: Creating new subject: $subjectName');
      }

      // Handle section if provided (case-insensitive check)
      if (sectionName != null && sectionName.isNotEmpty) {
        SectionEntity sectionEntity;
        String? existingSectionKey;

        // Find existing section with case-insensitive comparison
        for (final key in subjectEntity.sections.keys) {
          if (key.toLowerCase() == sectionName.toLowerCase()) {
            existingSectionKey = key;
            break;
          }
        }

        if (existingSectionKey != null) {
          sectionEntity = subjectEntity.sections[existingSectionKey]!;
          debugPrint('DEBUG: Section $sectionName already exists as "$existingSectionKey"');
          // Use the existing section key for consistency
          sectionName = existingSectionKey;
        } else {
          sectionEntity = SectionEntity();
          debugPrint('DEBUG: Creating new section: $sectionName');
        }

        // Handle topic if provided (case-insensitive check)
        if (topicName != null && topicName.isNotEmpty) {
          String? existingTopicKey;

          // Find existing topic with case-insensitive comparison
          for (final key in sectionEntity.topics.keys) {
            if (key.toLowerCase() == topicName.toLowerCase()) {
              existingTopicKey = key;
              break;
            }
          }

          if (existingTopicKey != null) {
            debugPrint('DEBUG: Topic $topicName already exists as "$existingTopicKey"');
            // Use the existing topic key for consistency
            topicName = existingTopicKey;
          } else {
            sectionEntity = sectionEntity.copyWith(
              topics: {...sectionEntity.topics, topicName: TopicEntity()}
            );
            debugPrint('DEBUG: Creating new topic: $topicName');
          }
        }

        // Update subject with section
        subjectEntity = subjectEntity.copyWith(
          sections: {...subjectEntity.sections, sectionName: sectionEntity}
        );
      }

      // Update year with subject
      yearEntity = yearEntity.copyWith(
        subjects: {...yearEntity.subjects, subjectName: subjectEntity}
      );

      // Update course with year
      courseEntity = courseEntity.copyWith(
        years: {...courseEntity.years, yearId: yearEntity}
      );

      // Update courses with course
      coursesEntity = coursesEntity.copyWith(
        courses: {...coursesEntity.courses, courseId: courseEntity}
      );

      // Save back to Firestore
      debugPrint('DEBUG: Saving updated course structure...');
      await docRef.set(coursesEntity.toJson());
      debugPrint('DEBUG: Course structure saved successfully');

    } catch (e, stackTrace) {
      debugPrint('DEBUG: ==================== ERROR ====================');
      debugPrint('DEBUG: Course structure creation failed');
      debugPrint('DEBUG: courseId="$courseId", yearId="$yearId"');
      debugPrint('DEBUG: subjectId="$subjectId", subjectName="$subjectName"');
      debugPrint('DEBUG: sectionName="$sectionName", topicName="$topicName"');
      debugPrint('DEBUG: Error type: ${e.runtimeType}');
      debugPrint('DEBUG: Error details: $e');
      debugPrint('DEBUG: Stack trace: $stackTrace');
      debugPrint('DEBUG: ===============================================');

      throw Exception('Failed to create course structure: ${e.toString()}');
    }
  }

  /// Create MCQ question from row data
  QuestionEntity? _createMCQQuestion(
    Map<String, dynamic> row,
    String courseId,
    String yearId,
    String subjectName,
    String? sectionName,
    String? topicName,
  ) {
    try {
      final questionText = _getValueFromRow(row, 'QUESTION')?.trim();
      final answer1 = _getValueFromRow(row, 'ANSWER 1')?.trim();
      final answer2 = _getValueFromRow(row, 'ANSWER 2')?.trim();
      final answer3 = _getValueFromRow(row, 'ANSWER 3')?.trim();
      final answer4 = _getValueFromRow(row, 'ANSWER 4')?.trim();
      final answer5 = _getValueFromRow(row, 'ANSWER 5')?.trim();
      final correctAnswerStr = _getValueFromRow(row, 'CORRECT ANSWER')?.trim();
      final explanation = _getValueFromRow(row, 'DETAILED EXPLANATION')?.trim();
      final difficultyStr = _getValueFromRow(row, 'DIFFICULTY RATING')?.trim();

      if (questionText == null || questionText.isEmpty) return null;

      // Parse correct answer (1-based to 0-based)
      int? correctAnswerIndex;
      if (correctAnswerStr != null) {
        final parsed = int.tryParse(correctAnswerStr);
        if (parsed != null && parsed >= 1 && parsed <= 5) {
          correctAnswerIndex = parsed - 1; // Convert to 0-based
        }
      }

      // Build answers list
      final answers = <String>[];
      if (answer1 != null && answer1.isNotEmpty) answers.add(answer1);
      if (answer2 != null && answer2.isNotEmpty) answers.add(answer2);
      if (answer3 != null && answer3.isNotEmpty) answers.add(answer3);
      if (answer4 != null && answer4.isNotEmpty) answers.add(answer4);
      if (answer5 != null && answer5.isNotEmpty) answers.add(answer5);

      if (answers.isEmpty || correctAnswerIndex == null || correctAnswerIndex >= answers.length) {
        return null;
      }

      return QuestionEntity(
        id: '', // Will be generated by Firestore
        question: questionText,
        type: QuestionType.mcq,
        options: answers,
        correctOptionIndex: correctAnswerIndex,
        explanation: explanation,
        courseId: courseId,
        yearId: yearId,
        subjectId: subjectName,
        section: sectionName,
        topic: topicName,
        difficulty: _parseDifficulty(difficultyStr),
        createdAt: DateTime.now(),
      );
    } catch (e) {
      return null;
    }
  }

  /// Create FlipCard question from row data
  QuestionEntity? _createFlipCardQuestion(
    Map<String, dynamic> row,
    String courseId,
    String yearId,
    String subjectName,
    String? sectionName,
    String? topicName,
  ) {
    try {
      final front = _getValueFromRow(row, 'Front')?.trim();
      final back = _getValueFromRow(row, 'Back')?.trim();
      final difficultyStr = _getValueFromRow(row, 'DIFFICULTY RATING')?.trim();

      if (front == null || front.isEmpty || back == null || back.isEmpty) {
        return null;
      }

      return QuestionEntity(
        id: '', // Will be generated by Firestore
        question: front,
        type: QuestionType.flipCard,
        answer: back, // FlipCard uses answer field
        courseId: courseId,
        yearId: yearId,
        subjectId: subjectName,
        section: sectionName,
        topic: topicName,
        difficulty: _parseDifficulty(difficultyStr),
        createdAt: DateTime.now(),
      );
    } catch (e) {
      return null;
    }
  }

  /// Get question type from detected headers
  String _getQuestionType() {
    return _detectQuestionType(_headers);
  }

  /// Get corrected course structure values after duplication checks
  Future<Map<String, String>> _getCorrectedCourseValues(String courseId, String yearId, String subjectName) async {
    try {
      final firestore = FirebaseFirestore.instance;
      final doc = await firestore.collection('course_catalog').doc('structure').get();

      if (!doc.exists) {
        // If no course catalog exists, return original values
        return {'yearId': yearId, 'subjectName': subjectName};
      }

      final data = doc.data()!;
      final coursesEntity = CoursesEntity.fromJson(data);

      final courseEntity = coursesEntity.courses[courseId];
      if (courseEntity == null) {
        // If course doesn't exist, return original values
        return {'yearId': yearId, 'subjectName': subjectName};
      }

      // Find existing year with case-insensitive comparison
      String correctedYearId = yearId;
      for (final key in courseEntity.years.keys) {
        if (key.toLowerCase() == yearId.toLowerCase()) {
          correctedYearId = key;
          break;
        }
      }

      // Find existing subject with case-insensitive comparison
      String correctedSubjectName = subjectName;
      final yearEntity = courseEntity.years[correctedYearId];
      if (yearEntity != null) {
        for (final key in yearEntity.subjects.keys) {
          if (key.toLowerCase() == subjectName.toLowerCase()) {
            correctedSubjectName = key;
            break;
          }
        }
      }

      return {
        'yearId': correctedYearId,
        'subjectName': correctedSubjectName,
      };
    } catch (e) {
      debugPrint('DEBUG: Error getting corrected course values: $e');
      // Return original values if there's an error
      return {'yearId': yearId, 'subjectName': subjectName};
    }
  }

  /// Save question to Firestore
  Future<void> _saveQuestionToFirestore(QuestionEntity question) async {
    debugPrint('DEBUG: Starting question save to Firestore...');
    debugPrint('DEBUG: Question type: ${question.type}');
    debugPrint('DEBUG: Course: ${question.courseId}, Year: ${question.yearId}, Subject: ${question.subjectId}');

    try {
      final firestore = FirebaseFirestore.instance;
      final docRef = firestore.collection('questions').doc();
      debugPrint('DEBUG: Generated document ID: ${docRef.id}');

      // Create question with generated ID
      final questionWithId = QuestionEntity(
        id: docRef.id,
        question: question.question,
        type: question.type,
        options: question.options,
        correctOptionIndex: question.correctOptionIndex,
        answer: question.answer,
        explanation: question.explanation,
        courseId: question.courseId,
        yearId: question.yearId,
        subjectId: question.subjectId,
        section: question.section,
        topic: question.topic,
        difficulty: question.difficulty,
        createdAt: question.createdAt,
      );

      debugPrint('DEBUG: Converting question to JSON...');
      final jsonData = questionWithId.toJson();
      debugPrint('DEBUG: JSON keys: ${jsonData.keys.toList()}');
      debugPrint('DEBUG: CreatedAt value: ${jsonData['createdAt']}');
      debugPrint('DEBUG: CreatedAt type: ${jsonData['createdAt'].runtimeType}');

      // Save to Firestore
      debugPrint('DEBUG: Saving to Firestore...');
      await docRef.set(jsonData);
      debugPrint('DEBUG: Question saved successfully with ID: ${docRef.id}');

    } catch (e, stackTrace) {
      debugPrint('DEBUG: ==================== QUESTION SAVE ERROR ====================');
      debugPrint('DEBUG: Failed to save question to Firestore');
      debugPrint('DEBUG: Question: ${question.question.substring(0, 50)}...');
      debugPrint('DEBUG: Error type: ${e.runtimeType}');
      debugPrint('DEBUG: Error details: $e');
      debugPrint('DEBUG: Stack trace: $stackTrace');
      debugPrint('DEBUG: =========================================================');
      rethrow;
    }
  }

  /// Parse difficulty rating
  TestDifficulty _parseDifficulty(String? difficultyStr) {
    if (difficultyStr == null) return TestDifficulty.easy;
    final parsed = int.tryParse(difficultyStr);
    if (parsed != null) {
      switch (parsed) {
        case 1: return TestDifficulty.easy;
        case 2: return TestDifficulty.medium;
        case 3:
        case 4:
        case 5: return TestDifficulty.hard;
        default: return TestDifficulty.easy;
      }
    }
    return TestDifficulty.easy; // Default difficulty
  }

  /// Show import results dialog
  void _showImportResults(ImportResults results) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Complete'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('✅ Successfully imported: ${results.successful} questions'),
            if (results.failed > 0) ...[
              const SizedBox(height: 8),
              Text('❌ Failed: ${results.failed} questions'),
              if (results.errors.isNotEmpty) ...[
                const SizedBox(height: 8),
                const Text('Errors:', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 4),
                Container(
                  height: 150,
                  width: double.maxFinite,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(8),
                    child: Text(
                      results.errors.join('\n'),
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ),
              ],
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (results.successful > 0) {
                context.pop(); // Go back to previous screen
              }
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
