// lib/features/tests/widgets/test_actions.dart

import 'package:entities/test_entity.dart';
import 'package:entities/test_enums.dart';
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../shared/utils/error_handler.dart';
import '../providers/simple_test_provider.dart';
import '../services/published_test_service.dart';

class TestActionMenu extends ConsumerWidget {
  final TestEntity test;

  const TestActionMenu({
    super.key,
    required this.test,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Since only admins can access the admin panel, no permission checks needed

    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert),
      onSelected: (value) {
        if (value == 'view') {
          _viewTest(context, test);
        } else if (value == 'edit') {
          _editTest(context, test);
        } else if (value == 'delete') {
          _deleteTest(context, ref, test);
        } else if (value == 'publish') {
          _publishTest(context, ref, test);
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem<String>(
          value: 'view',
          child: Row(
            children: [
              Icon(Icons.visibility, size: 16),
              SizedBox(width: 8),
              Text('View Details'),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 16),
              SizedBox(width: 8),
              Text('Edit'),
            ],
          ),
        ),
        if (test.status != TestStatus.published)
          const PopupMenuItem<String>(
            value: 'publish',
            child: Row(
              children: [
                Icon(Icons.publish, size: 16),
                SizedBox(width: 8),
                Text('Publish'),
              ],
            ),
          ),
        const PopupMenuDivider(),
        PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 16, color: Colors.red[600]),
              const SizedBox(width: 8),
              Text('Delete', style: TextStyle(color: Colors.red[600])),
            ],
          ),
        ),
      ],
    );
  }

  void _viewTest(BuildContext context, TestEntity test) {
    context.push('/view-test/${test.id}');
  }

  void _editTest(BuildContext context, TestEntity test) {
    context.push('/edit-test/${test.id}');
  }

  void _deleteTest(BuildContext context, WidgetRef ref, TestEntity test) {
    if (test.id == null) return;

    showDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissing during deletion
      builder: (dialogContext) => _DeleteTestDialog(
        testId: test.id!,
        testName: test.name,
        ref: ref,
      ),
    );
  }

  void _publishTest(BuildContext context, WidgetRef ref, TestEntity test) {
    if (test.id == null) {
      ErrorHandler.showErrorSnackBar(ref, 'Cannot publish test without an ID');
      return;
    }



    // Check if test is already published
    if (test.status == TestStatus.published) {
      ErrorHandler.showInfoSnackBar(ref, 'This test is already published');
      return;
    }

    // Check if test has questions
    if (test.questionIds.isEmpty) {
      ErrorHandler.showErrorSnackBar(ref, 'Cannot publish a test with no questions. Please add questions first.');
      return;
    }

    // Check if test has at least 5 questions
    if (test.questionIds.length < 5) {
      ErrorHandler.showErrorSnackBar(ref, 'Tests must have at least 5 questions to be published. Please add more questions.');
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Publish Test'),
        content: const Text(
            'Are you sure you want to publish this test? It will be available to students.'),
        actions: [
          TextButton(
            onPressed: () => context.pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              context.pop();

              // Create a ValueNotifier to track the current publishing step
              final ValueNotifier<String> publishingStep = ValueNotifier<String>("Preparing to publish test...");

              // Create a variable to track the dialog context
              BuildContext? dialogContext;

              // Set up a timeout to ensure the dialog is dismissed
              Timer? timeoutTimer;

              // Function to safely dismiss the dialog
              void safelyDismissDialog() {
                try {
                  // Cancel the timeout timer if it exists
                  timeoutTimer?.cancel();

                  // Only pop if the dialog context is valid and mounted
                  if (dialogContext != null && Navigator.of(dialogContext!, rootNavigator: true).canPop()) {
                    Navigator.of(dialogContext!, rootNavigator: true).pop();
                  }
                } catch (e) {
                  debugPrint('Error dismissing dialog: $e');
                }
              }

              if (context.mounted) {
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (innerContext) {
                    // Store the dialog context for later use
                    dialogContext = innerContext;

                    // Set up a timeout to dismiss the dialog after 30 seconds
                    // This is a safety mechanism in case something goes wrong
                    timeoutTimer = Timer(const Duration(seconds: 30), () {
                      debugPrint('Dialog timeout triggered - forcing dismiss');
                      safelyDismissDialog();

                      // Show an error message if the dialog times out
                      if (context.mounted) {
                        ErrorHandler.showErrorSnackBar(ref,
                          'Publishing operation timed out. Please try again.',
                          context: 'Publish Test');
                      }
                    });

                    return Dialog(
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const CircularProgressIndicator(),
                            const SizedBox(height: 16),
                            ValueListenableBuilder<String>(
                              valueListenable: publishingStep,
                              builder: (context, value, child) {
                                return Text(
                                  value,
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(fontSize: 16),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              }

              try {
                // Step 1: Assemble the published test
                publishingStep.value = "Step 1/3: Assembling published test...";
                final publishedTest = await PublishedTestService.assemblePublishedTest(
                  testId: test.id!,
                );

                // Step 2: Save the published test
                publishingStep.value = "Step 2/3: Saving published test...";
                await PublishedTestService.savePublishedTest(publishedTest);

                // Step 3: Update the test status to published
                publishingStep.value = "Step 3/3: Updating test status to published...";
                await ref.read(testNotifierProvider.notifier).publishTest(test.id!);

                // Final step: Complete
                publishingStep.value = "Test published successfully!";

                // Short delay to show the success message before closing the dialog
                await Future.delayed(const Duration(milliseconds: 500));

                // Safely dismiss the dialog
                safelyDismissDialog();

                if (context.mounted) {
                  ErrorHandler.showSuccessSnackBar(
                      ref, 'Test published successfully');
                }
              } catch (e) {
                // Update the dialog to show the error
                publishingStep.value = "Error: Publishing failed";
                await Future.delayed(const Duration(milliseconds: 500));

                // Always safely dismiss the loading dialog first
                safelyDismissDialog();

                // Handle specific error cases
                if (context.mounted) {
                  if (e is QuestionsVerificationException) {
                    _showVerificationErrorDialog(context, e);
                  } else if (e.toString().contains('at least 5 questions')) {
                    ErrorHandler.showErrorSnackBar(ref,
                      'Tests must have at least 5 questions to be published. Please add more questions.',
                      context: 'Publish Test');
                  } else if (e.toString().contains('permission-denied')) {
                    ErrorHandler.showErrorSnackBar(ref,
                      'Permission denied. You may not have the necessary permissions to publish tests.',
                      context: 'Publish Test');
                  } else if (e.toString().contains('network')) {
                    ErrorHandler.showErrorSnackBar(ref,
                      'Network error. Please check your internet connection and try again.',
                      context: 'Publish Test');
                  } else {
                    // Generic error handler for all other cases
                    // Convert the error to a string to ensure we're showing the actual error message
                    final errorMessage = e.toString();
                    ErrorHandler.showErrorSnackBar(ref, errorMessage,
                        context: 'Publish Test');
                  }
                }
              }
            },
            child: const Text('Publish'),
          ),
        ],
      ),
    );
  }

  void _showVerificationErrorDialog(
      BuildContext context, QuestionsVerificationException error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Publishing Failed'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'The test could not be published due to the following issues:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              if (error.missingQuestionIds.isNotEmpty) ...[
                const Text('Missing questions:',
                    style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red)),
                const SizedBox(height: 8),
                ...error.missingQuestionIds.map((id) => Text('• $id')),
                const SizedBox(height: 16),
                const Text(
                  'These questions may have been deleted. Please remove them from the test.',
                  style: TextStyle(fontStyle: FontStyle.italic),
                ),
                const SizedBox(height: 16),
              ],
              if (error.unpublishedQuestionIds.isNotEmpty) ...[
                const Text('Unpublished questions:',
                    style: TextStyle(fontWeight: FontWeight.bold, color: Colors.orange)),
                const SizedBox(height: 8),
                ...error.unpublishedQuestionIds.map((id) => Text('• $id')),
                const SizedBox(height: 16),
                const Text(
                  'These questions need to be published first. Go to the Questions section to publish them.',
                  style: TextStyle(fontStyle: FontStyle.italic),
                ),
              ],
              const SizedBox(height: 16),
              const Text(
                'All questions must exist and be published before you can publish this test.',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
          if (error.unpublishedQuestionIds.isNotEmpty)
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                context.push('/tests');
              },
              child: const Text('Go to Tests'),
            ),
        ],
      ),
    );
  }
}

/// Stateful dialog for test deletion with loading state
class _DeleteTestDialog extends ConsumerStatefulWidget {
  final String testId;
  final String testName;
  final WidgetRef ref;

  const _DeleteTestDialog({
    required this.testId,
    required this.testName,
    required this.ref,
  });

  @override
  ConsumerState<_DeleteTestDialog> createState() => _DeleteTestDialogState();
}

class _DeleteTestDialogState extends ConsumerState<_DeleteTestDialog> {
  bool _isDeleting = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Delete Test'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Are you sure you want to delete this test? This action cannot be undone.'),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Text(
              widget.testName,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isDeleting ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: _isDeleting ? null : _deleteTest,
          style: TextButton.styleFrom(foregroundColor: Colors.red),
          child: _isDeleting
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                ),
              )
            : const Text('Delete'),
        ),
      ],
    );
  }

  Future<void> _deleteTest() async {
    setState(() {
      _isDeleting = true;
    });

    try {
      await widget.ref.read(testNotifierProvider.notifier).deleteTest(widget.testId);

      if (mounted) {
        Navigator.of(context).pop();
        ErrorHandler.showSuccessSnackBar(
          widget.ref,
          'Test deleted successfully'
        );
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          _isDeleting = false;
        });
        ErrorHandler.showErrorSnackBar(
          widget.ref,
          error.toString(),
          context: 'Delete Test'
        );
      }
    }
  }
}
