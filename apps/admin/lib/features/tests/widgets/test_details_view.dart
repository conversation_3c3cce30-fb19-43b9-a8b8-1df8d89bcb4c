// apps/admin/lib/features/tests/widgets/test_details_view.dart

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:entities/test_enums.dart';
import 'package:go_router/go_router.dart';

import '../../../shared/widgets/status_messages.dart';
import '../providers/simple_test_provider.dart';
import '../../questions/providers/question_provider.dart';

class TestDetailsView extends ConsumerWidget {
  final String testId;

  const TestDetailsView({required this.testId, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final testAsync = ref.watch(singleTestProvider(testId));

    return testAsync.when(
      loading: () => const LoadingMessage(message: "Loading test details..."),
      error: (error, stack) => ErrorMessage(
        title: "Error Loading Test",
        message: "There was a problem retrieving the test details.",
        onRetry: () => ref.invalidate(singleTestProvider(testId)),
      ),
      data: (test) {
        if (test == null) {
          return const ErrorMessage(
            title: "Test Not Found",
            message: "The requested test could not be found.",
          );
        }

        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Test Header Card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              test.name,
                              style: const TextStyle(
                                fontSize: 28,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          _buildStatusChip(test.status),
                        ],
                      ),
                      const SizedBox(height: 16),
                      _buildInfoRow('Type', test.type.name.toUpperCase()),
                      _buildInfoRow('Difficulty', test.difficulty.name.toUpperCase()),
                      _buildInfoRow('Subject', test.subjectId),
                      _buildInfoRow('Course', test.courseId),
                      _buildInfoRow('Year', test.yearId),
                      _buildInfoRow('Duration', '${test.duration} minutes'),
                      _buildInfoRow('Questions', '${test.questionIds.length} questions'),
                      if (test.createdAt != null)
                        _buildInfoRow('Created', _formatDate(test.createdAt!)),
                      if (test.updatedAt != null)
                        _buildInfoRow('Last Updated', _formatDate(test.updatedAt!)),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Note: TestEntity doesn't have a description field, so we'll skip this section

              // Questions Card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'Questions',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            '${test.questionIds.length} questions',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      if (test.questionIds.isEmpty)
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.orange[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.orange[200]!),
                          ),
                          child: Column(
                            children: [
                              Icon(
                                Icons.quiz_outlined,
                                size: 48,
                                color: Colors.orange[400],
                              ),
                              const SizedBox(height: 12),
                              Text(
                                'No questions added yet',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.orange[700],
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Add questions to this test to make it available for students.',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.orange[600],
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        )
                      else
                        _buildQuestionsList(context, ref, test.questionIds),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuestionsList(BuildContext context, WidgetRef ref, List<String> questionIds) {
    return Column(
      children: questionIds.asMap().entries.map((entry) {
        final index = entry.key;
        final questionId = entry.value;

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Consumer(
            builder: (context, ref, child) {
              final questionAsync = ref.watch(getQuestionProvider(questionId));

              return questionAsync.when(
                loading: () => const ListTile(
                  leading: CircularProgressIndicator(),
                  title: Text('Loading question...'),
                ),
                error: (error, stack) => ListTile(
                  leading: Icon(Icons.error, color: Colors.red[400]),
                  title: Text('Error loading question'),
                  subtitle: Text('Question ID: $questionId'),
                ),
                data: (question) {
                  if (question == null) {
                    return ListTile(
                      leading: Icon(Icons.help_outline, color: Colors.orange[400]),
                      title: const Text('Question not found'),
                      subtitle: Text('Question ID: $questionId'),
                    );
                  }

                  return ListTile(
                    leading: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: Colors.blue[100],
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Text(
                          '${index + 1}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[700],
                          ),
                        ),
                      ),
                    ),
                    title: Text(
                      question.question.length > 80
                          ? '${question.question.substring(0, 80)}...'
                          : question.question,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    subtitle: Text(
                      question.type.name.toUpperCase(), // Difficulty removed from questions
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.visibility),
                      onPressed: () => context.push('/view-question/$questionId'),
                      tooltip: 'View Question',
                    ),
                  );
                },
              );
            },
          ),
        );
      }).toList(),
    );
  }

  Widget _buildStatusChip(TestStatus status) {
    Color color;
    String label;

    switch (status) {
      case TestStatus.draft:
        color = Colors.orange;
        label = 'DRAFT';
        break;
      case TestStatus.published:
        color = Colors.green;
        label = 'PUBLISHED';
        break;
      case TestStatus.review:
        color = Colors.blue;
        label = 'REVIEW';
        break;
    }

    return Chip(
      label: Text(
        label,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
      backgroundColor: color,
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} at ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
