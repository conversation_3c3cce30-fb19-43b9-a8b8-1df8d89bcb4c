// apps/admin/lib/features/tests/widgets/simple_test_card.dart

import 'package:entities/test_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../shared/widgets/app_button.dart';
import '../providers/simple_test_provider.dart';

class SimpleTestCard extends ConsumerWidget {
  final TestEntity test;

  const SimpleTestCard({
    super.key,
    required this.test,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        test.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${test.courseId} • ${test.yearId} • ${test.subjectId}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                _StatusBadge(status: test.status),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                _InfoChip(
                  icon: Icons.quiz,
                  label: test.type.name.toUpperCase(),
                ),
                const SizedBox(width: 8),
                _InfoChip(
                  icon: Icons.timer,
                  label: '${test.duration} min',
                ),
                const SizedBox(width: 8),
                _InfoChip(
                  icon: Icons.help_outline,
                  label: '${test.questionIds.length} questions',
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                AppButton(
                  label: 'View',
                  onPressed: () => context.push('/view-test/${test.id}'),
                  variant: AppButtonVariant.outlined,
                ),
                const SizedBox(width: 8),
                AppButton(
                  label: 'Edit',
                  onPressed: () => context.push('/edit-test/${test.id}'),
                  variant: AppButtonVariant.outlined,
                ),
                const SizedBox(width: 8),
                if (test.status.name == 'draft' && test.questionIds.length >= 5)
                  AppButton(
                    label: 'Publish',
                    onPressed: () => _publishTest(context, ref),
                    variant: AppButtonVariant.filled,
                  ),
                const Spacer(),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleMenuAction(context, ref, value),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _publishTest(BuildContext context, WidgetRef ref) async {
    try {
      await ref.read(testNotifierProvider.notifier).publishTest(test.id!);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Test published successfully!')),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to publish test. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _handleMenuAction(BuildContext context, WidgetRef ref, String action) async {
    switch (action) {
      case 'delete':
        final confirmed = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Delete Test'),
            content: Text('Are you sure you want to delete "${test.name}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
        );

        if (confirmed == true && context.mounted) {
          try {
            await ref.read(testNotifierProvider.notifier).deleteTest(test.id!);
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Test deleted successfully!')),
              );
            }
          } catch (e) {
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Failed to delete test. Please try again.'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        }
        break;
    }
  }
}

class _StatusBadge extends StatelessWidget {
  final dynamic status;

  const _StatusBadge({required this.status});

  @override
  Widget build(BuildContext context) {
    final statusName = status.toString().split('.').last;
    Color color;
    
    switch (statusName) {
      case 'draft':
        color = Colors.orange;
        break;
      case 'published':
        color = Colors.green;
        break;
      case 'review':
        color = Colors.blue;
        break;
      default:
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        statusName.toUpperCase(),
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

class _InfoChip extends StatelessWidget {
  final IconData icon;
  final String label;

  const _InfoChip({
    required this.icon,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}
