// packages/entities/lib/course_entity.dart

import 'package:entities/entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'year_entity.dart';

part 'course_entity.freezed.dart';
part 'course_entity.g.dart';

/// CourseEntity represents a course in the hierarchical structure.
/// The course name is the map key, not stored as a property.
/// Structure: courses[courseName] -> years[yearName] -> subjects[subjectName] -> sections[sectionName] -> topics[topicName] -> tests
@Freezed(toJson: true)
class CourseEntity extends Entity with _$CourseEntity {
  factory CourseEntity({
    @Default({}) Map<String, YearEntity> years,
  }) = _CourseEntity;

  @override
  factory CourseEntity.fromJson(Map<String, dynamic> json) => reportAnyJsonBugs(_$CourseEntityFromJson, json);
}

/// Extension methods to provide backward compatibility
extension CourseEntityExtensions on CourseEntity {
  /// Get the course ID (name) - this is a placeholder since the actual name is stored as map key
  /// This property is provided for backward compatibility but should not be used
  /// The actual course name should be obtained from the map key where this entity is stored
  String? get id => null; // Cannot determine ID without context of the map key
}
