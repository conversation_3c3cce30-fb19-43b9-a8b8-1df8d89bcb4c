// packages/entities/lib/year_entity.dart

import 'package:entities/entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'subject_entity.dart';

part 'year_entity.freezed.dart';
part 'year_entity.g.dart';

/// YearEntity represents a year within a course.
/// The year name is the map key, not stored as a property.
/// Structure: years[yearName] -> subjects[subjectName] -> sections[sectionName] -> topics[topicName] -> tests
@Freezed(toJson: true)
class YearEntity extends Entity with _$YearEntity {
  factory YearEntity({
    @Default({}) Map<String, SubjectEntity> subjects,
  }) = _YearEntity;

  @override
  factory YearEntity.fromJson(Map<String, dynamic> json) => reportAnyJsonBugs(_$YearEntityFromJson, json);
}

/// Extension methods to provide backward compatibility
extension YearEntityExtensions on YearEntity {
  /// Get the year ID (name) - this is a placeholder since the actual name is stored as map key
  /// This property is provided for backward compatibility but should not be used
  /// The actual year name should be obtained from the map key where this entity is stored
  String? get id => null; // Cannot determine ID without context of the map key
}
