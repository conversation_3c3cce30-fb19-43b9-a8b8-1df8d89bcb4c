// packages/entities/lib/subject_entity.dart

import 'package:entities/entity.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import 'section_entity.dart';
import 'published_test_entity.dart';
import 'topic_entity.dart';

part 'subject_entity.freezed.dart';
part 'subject_entity.g.dart';

/// SubjectEntity represents a subject within a year.
/// The subject name is the map key, not stored as a property.
/// Structure: subjects[subjectName] -> sections[sectionName] -> topics[topicName] -> tests
@Freezed(toJson: true)
class SubjectEntity extends Entity with _$SubjectEntity {
  factory SubjectEntity({
    @Default({}) Map<String, SectionEntity> sections,
  }) = _SubjectEntity;

  @override
  factory SubjectEntity.fromJson(Map<String, dynamic> json) => reportAnyJsonBugs(_$SubjectEntityFromJson, json);
}

/// Extension methods to provide backward compatibility for accessing tests
extension SubjectEntityExtensions on SubjectEntity {
  /// Get the subject name - this is a placeholder since the actual name is stored as map key
  /// This property is provided for backward compatibility but should not be used
  /// The actual subject name should be obtained from the map key where this entity is stored
  String? get name => null; // Cannot determine name without context of the map key

  /// Get all tests from all sections and topics in this subject
  Map<String, PublishedTestEntity> get tests {
    final allTests = <String, PublishedTestEntity>{};

    for (final section in sections.values) {
      for (final topic in section.topics.values) {
        allTests.addAll(topic.tests);
      }
    }

    return allTests;
  }

  /// Create a copy of this SubjectEntity with modified tests
  /// This is a convenience method for backward compatibility
  SubjectEntity copyWithTests(Map<String, PublishedTestEntity> tests) {
    // For simplicity, we'll put all tests in a default section and topic
    const defaultSectionName = 'General';
    const defaultTopicName = 'General';

    final defaultTopic = TopicEntity(tests: tests);
    final defaultSection = SectionEntity(topics: {defaultTopicName: defaultTopic});

    return SubjectEntity(sections: {defaultSectionName: defaultSection});
  }
}
