{"version": 3, "file": "index.esm.js", "sources": ["../../src/logger.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport type LogLevelString =\n  | 'debug'\n  | 'verbose'\n  | 'info'\n  | 'warn'\n  | 'error'\n  | 'silent';\n\nexport interface LogOptions {\n  level: LogLevelString;\n}\n\nexport type LogCallback = (callbackParams: LogCallbackParams) => void;\n\nexport interface LogCallbackParams {\n  level: LogLevelString;\n  message: string;\n  args: unknown[];\n  type: string;\n}\n\n/**\n * A container for all of the Logger instances\n */\nexport const instances: Logger[] = [];\n\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nexport enum LogLevel {\n  DEBUG,\n  VERBOSE,\n  INFO,\n  WARN,\n  ERROR,\n  SILENT\n}\n\nconst levelStringToEnum: { [key in LogLevelString]: LogLevel } = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n\n/**\n * The default log level\n */\nconst defaultLogLevel: LogLevel = LogLevel.INFO;\n\n/**\n * We allow users the ability to pass their own log handler. We will pass the\n * type of log, the current log level, and any other arguments passed (i.e. the\n * messages that the user wants to log) to this function.\n */\nexport type LogHandler = (\n  loggerInstance: Logger,\n  logType: LogLevel,\n  ...args: unknown[]\n) => void;\n\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler: LogHandler = (instance, logType, ...args): void => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType as keyof typeof ConsoleMethod];\n  if (method) {\n    console[method as 'log' | 'info' | 'warn' | 'error'](\n      `[${now}]  ${instance.name}:`,\n      ...args\n    );\n  } else {\n    throw new Error(\n      `Attempted to log a message with an invalid logType (value: ${logType})`\n    );\n  }\n};\n\nexport class Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(public name: string) {\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n\n  /**\n   * The log level of the given Logger instance.\n   */\n  private _logLevel = defaultLogLevel;\n\n  get logLevel(): LogLevel {\n    return this._logLevel;\n  }\n\n  set logLevel(val: LogLevel) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val: LogLevel | LogLevelString): void {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n\n  /**\n   * The main (internal) log handler for the Logger instance.\n   * Can be set to a new function in internal package code but not by user.\n   */\n  private _logHandler: LogHandler = defaultLogHandler;\n  get logHandler(): LogHandler {\n    return this._logHandler;\n  }\n  set logHandler(val: LogHandler) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n\n  /**\n   * The optional, additional, user-defined log handler for the Logger instance.\n   */\n  private _userLogHandler: LogHandler | null = null;\n  get userLogHandler(): LogHandler | null {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val: LogHandler | null) {\n    this._userLogHandler = val;\n  }\n\n  /**\n   * The functions below are all based on the `console` interface\n   */\n\n  debug(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args: unknown[]): void {\n    this._userLogHandler &&\n      this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args: unknown[]): void {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\n\nexport function setLogLevel(level: LogLevelString | LogLevel): void {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\n\nexport function setUserLogHandler(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  for (const instance of instances) {\n    let customLogLevel: LogLevel | null = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (\n        instance: Logger,\n        level: LogLevel,\n        ...args: unknown[]\n      ) => {\n        const message = args\n          .map(arg => {\n            if (arg == null) {\n              return null;\n            } else if (typeof arg === 'string') {\n              return arg;\n            } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n              return arg.toString();\n            } else if (arg instanceof Error) {\n              return arg.message;\n            } else {\n              try {\n                return JSON.stringify(arg);\n              } catch (ignored) {\n                return null;\n              }\n            }\n          })\n          .filter(arg => arg)\n          .join(' ');\n        if (level >= (customLogLevel ?? instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase() as LogLevelString,\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;AAeG;AAuBH;;AAEG;AACI,MAAM,SAAS,GAAa,EAAE,CAAC;AAEtC;;;;;;;;;;AAUG;IACS,SAOX;AAPD,CAAA,UAAY,QAAQ,EAAA;AAClB,IAAA,QAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACL,IAAA,QAAA,CAAA,QAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO,CAAA;AACP,IAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACJ,IAAA,QAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACL,IAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM,CAAA;AACR,CAAC,EAPW,QAAQ,KAAR,QAAQ,GAOnB,EAAA,CAAA,CAAA,CAAA;AAED,MAAM,iBAAiB,GAA0C;IAC/D,OAAO,EAAE,QAAQ,CAAC,KAAK;IACvB,SAAS,EAAE,QAAQ,CAAC,OAAO;IAC3B,MAAM,EAAE,QAAQ,CAAC,IAAI;IACrB,MAAM,EAAE,QAAQ,CAAC,IAAI;IACrB,OAAO,EAAE,QAAQ,CAAC,KAAK;IACvB,QAAQ,EAAE,QAAQ,CAAC,MAAM;CAC1B,CAAC;AAEF;;AAEG;AACH,MAAM,eAAe,GAAa,QAAQ,CAAC,IAAI,CAAC;AAahD;;;;;AAKG;AACH,MAAM,aAAa,GAAG;AACpB,IAAA,CAAC,QAAQ,CAAC,KAAK,GAAG,KAAK;AACvB,IAAA,CAAC,QAAQ,CAAC,OAAO,GAAG,KAAK;AACzB,IAAA,CAAC,QAAQ,CAAC,IAAI,GAAG,MAAM;AACvB,IAAA,CAAC,QAAQ,CAAC,IAAI,GAAG,MAAM;AACvB,IAAA,CAAC,QAAQ,CAAC,KAAK,GAAG,OAAO;CAC1B,CAAC;AAEF;;;;AAIG;AACH,MAAM,iBAAiB,GAAe,CAAC,QAAQ,EAAE,OAAO,EAAE,GAAG,IAAI,KAAU;AACzE,IAAA,IAAI,OAAO,GAAG,QAAQ,CAAC,QAAQ,EAAE;QAC/B,OAAO;KACR;IACD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;AACrC,IAAA,MAAM,MAAM,GAAG,aAAa,CAAC,OAAqC,CAAC,CAAC;IACpE,IAAI,MAAM,EAAE;AACV,QAAA,OAAO,CAAC,MAA2C,CAAC,CAClD,IAAI,GAAG,CAAA,GAAA,EAAM,QAAQ,CAAC,IAAI,CAAG,CAAA,CAAA,EAC7B,GAAG,IAAI,CACR,CAAC;KACH;SAAM;AACL,QAAA,MAAM,IAAI,KAAK,CACb,8DAA8D,OAAO,CAAA,CAAA,CAAG,CACzE,CAAC;KACH;AACH,CAAC,CAAC;MAEW,MAAM,CAAA;AACjB;;;;;AAKG;AACH,IAAA,WAAA,CAAmB,IAAY,EAAA;QAAZ,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;AAO/B;;AAEG;QACK,IAAS,CAAA,SAAA,GAAG,eAAe,CAAC;AAkBpC;;;AAGG;QACK,IAAW,CAAA,WAAA,GAAe,iBAAiB,CAAC;AAWpD;;AAEG;QACK,IAAe,CAAA,eAAA,GAAsB,IAAI,CAAC;AA7ChD;;AAEG;AACH,QAAA,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACtB;AAOD,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IAED,IAAI,QAAQ,CAAC,GAAa,EAAA;AACxB,QAAA,IAAI,EAAE,GAAG,IAAI,QAAQ,CAAC,EAAE;AACtB,YAAA,MAAM,IAAI,SAAS,CAAC,kBAAkB,GAAG,CAAA,0BAAA,CAA4B,CAAC,CAAC;SACxE;AACD,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;KACtB;;AAGD,IAAA,WAAW,CAAC,GAA8B,EAAA;AACxC,QAAA,IAAI,CAAC,SAAS,GAAG,OAAO,GAAG,KAAK,QAAQ,GAAG,iBAAiB,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;KACzE;AAOD,IAAA,IAAI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;IACD,IAAI,UAAU,CAAC,GAAe,EAAA;AAC5B,QAAA,IAAI,OAAO,GAAG,KAAK,UAAU,EAAE;AAC7B,YAAA,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC,CAAC;SAC1E;AACD,QAAA,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC;KACxB;AAMD,IAAA,IAAI,cAAc,GAAA;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC;KAC7B;IACD,IAAI,cAAc,CAAC,GAAsB,EAAA;AACvC,QAAA,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC;KAC5B;AAED;;AAEG;IAEH,KAAK,CAAC,GAAG,IAAe,EAAA;AACtB,QAAA,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;AAC5E,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;KACjD;IACD,GAAG,CAAC,GAAG,IAAe,EAAA;AACpB,QAAA,IAAI,CAAC,eAAe;AAClB,YAAA,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;AACxD,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;KACnD;IACD,IAAI,CAAC,GAAG,IAAe,EAAA;AACrB,QAAA,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AAC3E,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;KAChD;IACD,IAAI,CAAC,GAAG,IAAe,EAAA;AACrB,QAAA,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AAC3E,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;KAChD;IACD,KAAK,CAAC,GAAG,IAAe,EAAA;AACtB,QAAA,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;AAC5E,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;KACjD;AACF,CAAA;AAEK,SAAU,WAAW,CAAC,KAAgC,EAAA;AAC1D,IAAA,SAAS,CAAC,OAAO,CAAC,IAAI,IAAG;AACvB,QAAA,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC1B,KAAC,CAAC,CAAC;AACL,CAAC;AAEe,SAAA,iBAAiB,CAC/B,WAA+B,EAC/B,OAAoB,EAAA;AAEpB,IAAA,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;QAChC,IAAI,cAAc,GAAoB,IAAI,CAAC;AAC3C,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE;AAC5B,YAAA,cAAc,GAAG,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACnD;AACD,QAAA,IAAI,WAAW,KAAK,IAAI,EAAE;AACxB,YAAA,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC;SAChC;aAAM;YACL,QAAQ,CAAC,cAAc,GAAG,CACxB,QAAgB,EAChB,KAAe,EACf,GAAG,IAAe,KAChB;gBACF,MAAM,OAAO,GAAG,IAAI;qBACjB,GAAG,CAAC,GAAG,IAAG;AACT,oBAAA,IAAI,GAAG,IAAI,IAAI,EAAE;AACf,wBAAA,OAAO,IAAI,CAAC;qBACb;AAAM,yBAAA,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AAClC,wBAAA,OAAO,GAAG,CAAC;qBACZ;yBAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,SAAS,EAAE;AAC9D,wBAAA,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;qBACvB;AAAM,yBAAA,IAAI,GAAG,YAAY,KAAK,EAAE;wBAC/B,OAAO,GAAG,CAAC,OAAO,CAAC;qBACpB;yBAAM;AACL,wBAAA,IAAI;AACF,4BAAA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;yBAC5B;wBAAC,OAAO,OAAO,EAAE;AAChB,4BAAA,OAAO,IAAI,CAAC;yBACb;qBACF;AACH,iBAAC,CAAC;AACD,qBAAA,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC;qBAClB,IAAI,CAAC,GAAG,CAAC,CAAC;gBACb,IAAI,KAAK,KAAK,cAAc,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAClD,oBAAA,WAAW,CAAC;AACV,wBAAA,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,WAAW,EAAoB;wBACtD,OAAO;wBACP,IAAI;wBACJ,IAAI,EAAE,QAAQ,CAAC,IAAI;AACpB,qBAAA,CAAC,CAAC;iBACJ;AACH,aAAC,CAAC;SACH;KACF;AACH;;;;"}